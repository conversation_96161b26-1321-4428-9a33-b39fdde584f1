import os
import argparse
import cv2
import numpy as np
from PIL import Image

import torch
import torch.nn as nn
from torch.autograd import Variable

from models.sdc_net2d import *
import skimage.measure

from tqdm import tqdm
import pdb
import glob
import pickle

# set gpu usage
os.environ["CUDA_DEVICE_ORDER"]="PCI_BUS_ID"
os.environ["CUDA_VISIBLE_DEVICES"]="1"


parser = argparse.ArgumentParser()
parser.add_argument('--pretrained', default='../pretrained_models/sdc_cityscapes_vrec.pth.tar', type=str, metavar='PATH', help='path to trained video reconstruction checkpoint')
parser.add_argument('--flownet2_checkpoint', default='../pretrained_models/FlowNet2_checkpoint.pth.tar', type=str, metavar='PATH', help='path to flownet-2 best checkpoint')
parser.add_argument('--source_dir', default='~/research/flownet2-pytorch/frames/2011_09_26/2011_09_26_drive_0002_extract/image_03/data/', type=str, help='directory for data (default: Cityscapes root directory)')
parser.add_argument('--target_dir', default='Cityscapes/cs_aug/kitti/', type=str, help='directory to save augmented data')
parser.add_argument('--sequence_length', default=2, type=int, metavar="SEQUENCE_LENGTH",
                    help='number of interpolated frames (default : 2)')
parser.add_argument("--rgb_max", type=float, default = 255.)
parser.add_argument('--fp16', action='store_true', help='Run model in pseudo-fp16 mode (fp16 storage fp32 math).')
parser.add_argument('--propagate', type=int, default=3, help='propagate how many steps')
parser.add_argument('--vis', action='store_true', default=False, help='augment color encoded segmentation map')

def get_model():
    model = SDCNet2DRecon(args)
    checkpoint = torch.load(args.pretrained)
    args.start_epoch = 0 if 'epoch' not in checkpoint else checkpoint['epoch']
    state_dict = checkpoint if 'state_dict' not in checkpoint else checkpoint['state_dict']
    model.load_state_dict(state_dict, strict=False)
    print("Loaded checkpoint '{}' (at epoch {})".format(args.pretrained, args.start_epoch))
    return model

def get_data(img1_dir, img2_dir, img3_dir):
    img1_rgb = cv2.imread(img1_dir)
    img2_rgb = cv2.imread(img2_dir)
    img3_rgb = cv2.imread(img3_dir)

    ## centercrop to nearest integer multiple of 64
    #     render_size[0] = ( (frame_size[0])//64 ) * 64
    #     render_size[1] = ( (frame_size[1])//64 ) * 64
    #             th, tw = crop_size
    #     h, w = image_size
    # def __call__(self, img):
    #     return img[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]
    h, w = img1_rgb.shape[0], img1_rgb.shape[1]
    th = (img1_rgb.shape[0]) // 64 * 64
    tw = (img1_rgb.shape[1]) // 64 * 64
    img1_rgb = img1_rgb[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]
    img2_rgb = img2_rgb[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]
    img3_rgb = img3_rgb[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]

    # pdb.set_trace()

    img1_rgb = img1_rgb.transpose((2,0,1))
    img2_rgb = img2_rgb.transpose((2,0,1))
    img3_rgb = img3_rgb.transpose((2,0,1))


    img1_rgb = np.expand_dims(img1_rgb, axis=0)
    img2_rgb = np.expand_dims(img2_rgb, axis=0)
    img3_rgb = np.expand_dims(img3_rgb, axis=0)


    img1_rgb = torch.from_numpy(img1_rgb.astype(np.float32))
    img2_rgb = torch.from_numpy(img2_rgb.astype(np.float32))
    img3_rgb = torch.from_numpy(img3_rgb.astype(np.float32))
    # gt2_rgb = torch.from_numpy(gt2_rgb.astype(np.float32))
    # gt2_labelid = torch.from_numpy(gt2_labelid.astype(np.float32))

    # return img1_rgb, img2_rgb, img3_rgb, gt2_rgb, gt2_labelid
    return img1_rgb, img2_rgb, img3_rgb

def remove_files_in_folder(folder):
    for f in os.listdir(folder):
        os.remove(os.path.join(folder, f))

def one_step_augmentation(model):
    split_dir = os.path.expanduser(args.source_dir)
    remove_files_in_folder(args.target_dir) ## overwrite target dir with new pred images
    # frames = os.listdir(split_dir)
    frames = glob.glob(split_dir + "/*.png") # only retrieve the png files in folder
    frames.sort()
    counter = 0

    for frame in tqdm(frames):
        # seq_info = frame.split("_")
        seq_info = frame.split("/")


        if counter == 0 or counter == len(frames) - 1:
            counter += 1
            continue

        # pdb.set_trace()

        counter += 1

        # seq_id2 = frame.split(".")[0]
        if seq_info[-1].split(".")[0].split("_")[-1] == "leftImg8bit":
            # image from the acquired data
            seq_id2 = seq_info[-1].split("_")[-2]
            seq_id1 = "%06d" % (int(seq_id2) - 1)
            seq_id3 = "%06d" % (int(seq_id2) + 1)
            name_im1 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id1 + "_leftImg8bit.png"
            source_im1 = os.path.join(split_dir, name_im1)
            name_im2 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id2 + "_leftImg8bit.png"
            source_im2 = os.path.join(split_dir, name_im2)
            name_im3 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id3 + "_leftImg8bit.png"
            source_im3 = os.path.join(split_dir, name_im3)
        else:
            # self provided images with only number 000000.png 00001.png ...
            seq_id2 = seq_info[-1].split(".")[0]
            seq_id1 = "%010d" % (int(seq_id2) - 1)
            seq_id3 = "%010d" % (int(seq_id2) + 1)
            source_im1 = os.path.join(split_dir, seq_id1 + ".png")
            # source_im2 = os.path.join(split_dir, frame)
            source_im2 = os.path.join(split_dir, seq_id2 + ".png")
            source_im3 = os.path.join(split_dir, seq_id3 + ".png")
        # pdb.set_trace()


        # pdb.set_trace()

        img1_rgb, img2_rgb, img3_rgb = get_data(source_im1, source_im2, source_im3)

        img1_rgb = Variable(img1_rgb).contiguous().cuda()
        img2_rgb = Variable(img2_rgb).contiguous().cuda()
        img3_rgb = Variable(img3_rgb).contiguous().cuda()

        input_dict = {}
        input_dict['image'] = [img1_rgb, img2_rgb, img3_rgb]

        # pdb.set_trace()

        _, pred3_rgb, _ = model(input_dict)
        pred3_rgb_img = (pred3_rgb.data.cpu().numpy().squeeze().transpose(1,2,0)).astype(np.uint8)

        if not os.path.exists(args.target_dir):
            os.makedirs(args.target_dir)
        target_im3 = os.path.join(args.target_dir, seq_id3 + ".png")

        # resize here to match gt size
        # gt_img_shape = cv2.imread(source_im2).shape
        # pred3_rgb_img = cv2.resize(pred3_rgb_img, (gt_img_shape[1], gt_img_shape[0]))

        cv2.imwrite(target_im3, pred3_rgb_img)


### multi step
def multi_step_augmentation(model, propagate=5):
    split_dir = os.path.expanduser(args.source_dir)
    # frames = os.listdir(split_dir)
    frames = glob.glob(split_dir + "/*.png") # only retrieve the png files in folder
    frames.sort()
    counter = 0

    # for idx, frame in enumerate(tqdm(frames)):
    for idx in tqdm(range(1, len(frames) - propagate, propagate)):
        for propage in range(propagate):

            frame = frames[idx]
            # seq_info = frame.split("_")
            seq_info = frame.split("/")
            # pdb.set_trace()

            counter += 1

            # seq_id2 = frame.split(".")[0]
            if seq_info[-1].split(".")[0].split("_")[-1] == "leftImg8bit":
                # image from the acquired data
                if propage == 0:
                    seq_id2 = seq_info[-1].split("_")[-2]
                    seq_id1 = "%06d" % (int(seq_id2) - 1)
                    seq_id3 = "%06d" % (int(seq_id2) + 1)
                    name_im1 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id1 + "_leftImg8bit.png"
                    source_im1 = os.path.join(split_dir, name_im1)
                    name_im2 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id2 + "_leftImg8bit.png"
                    source_im2 = os.path.join(split_dir, name_im2)
                    name_im3 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id3 + "_leftImg8bit.png"
                    source_im3 = os.path.join(split_dir, name_im3)
                if propage == 1:
                    source_im2 = os.path.join(args.target_dir, seq_id3 + ".png")
                    seq_id1 = "%06d" % (int(seq_id3) - 1)
                    name_im1 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id1 + "_leftImg8bit.png"
                    source_im1 = os.path.join(split_dir, name_im1)
                    seq_id3 = "%06d" % (int(seq_id3) + 1)
                    name_im3 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id3 + "_leftImg8bit.png"
                    source_im3 = os.path.join(split_dir, name_im3)
                elif propage > 1:
                    seq_id1 = "%06d" % (int(seq_id3) - 1)
                    source_im1 = os.path.join(args.target_dir, seq_id1 + ".png")
                    source_im2 = os.path.join(args.target_dir, seq_id3 + ".png")
                    seq_id3 = "%06d" % (int(seq_id3) + 1)
                    name_im3 = ("_").join(seq_info[-1].split("_")[:3]) + "_" + seq_id3 + "_leftImg8bit.png"
                    source_im3 = os.path.join(split_dir, name_im3)


            else:
                # self provided images with only number 000000.png 00001.png ...
                seq_id2 = seq_info[-1].split(".")[0]
                seq_id1 = "%010d" % (int(seq_id2) - 1)
                seq_id3 = "%010d" % (int(seq_id2) + 1)
                source_im1 = os.path.join(split_dir, seq_id1 + ".png")
                # source_im2 = os.path.join(split_dir, frame)
                source_im2 = os.path.join(split_dir, seq_id2 + ".png")
                source_im3 = os.path.join(split_dir, seq_id3 + ".png")
            # pdb.set_trace()

            print("source_im1: {}\nsource_im2: {}\nsource_im3: {}\n".format(source_im1, source_im2, source_im3))
            # pdb.set_trace()

            img1_rgb, img2_rgb, img3_rgb = get_data(source_im1, source_im2, source_im3)

            img1_rgb = Variable(img1_rgb).contiguous().cuda()
            img2_rgb = Variable(img2_rgb).contiguous().cuda()
            img3_rgb = Variable(img3_rgb).contiguous().cuda()

            input_dict = {}
            input_dict['image'] = [img1_rgb, img2_rgb, img3_rgb]

            # pdb.set_trace()

            _, pred3_rgb, _ = model(input_dict)
            pred3_rgb_img = (pred3_rgb.data.cpu().numpy().squeeze().transpose(1,2,0)).astype(np.uint8)

            if not os.path.exists(args.target_dir):
                os.makedirs(args.target_dir)
            target_im3 = os.path.join(args.target_dir, seq_id3 + ".png")
            cv2.imwrite(target_im3, pred3_rgb_img)

def compute_metrics():
    # pred_frames = os.listdir(args.target_dir)
    # gt_frames = os.listdir(os.path.expanduser(args.source_dir))
    pred_frames = glob.glob(args.target_dir + "/*.png")
    gt_frames = glob.glob(os.path.expanduser(args.source_dir + "/*.png"))
    pred_frames.sort()
    gt_frames.sort()
    mean_psnr, mean_ssim = 0, 0

    for i in range(len(pred_frames)):
        print("gt frame : {} , pred_frame : {}".format(gt_frames[i+2], pred_frames[i]))
        # pdb.set_trace()
        # gt_img = cv2.cvtColor(cv2.imread(gt_frames[i + 2]), cv2.COLOR_BGR2GRAY).astype(float)
        # pred_img = cv2.cvtColor(cv2.imread(pred_frames[i]), cv2.COLOR_BGR2GRAY).astype(float)

        ## convert to YUV to compute stats
        gt_img = cv2.cvtColor(cv2.imread(gt_frames[i + 2]), cv2.COLOR_BGR2YUV).astype(float)
        pred_img = cv2.cvtColor(cv2.imread(pred_frames[i]), cv2.COLOR_BGR2YUV).astype(float)

        # rgb image
        # gt_img = cv2.imread(gt_frames[i + 2]).astype(float)
        # pred_img = cv2.imread(pred_frames[i]).astype(float)


        # pred_img = cv2.resize(pred_img, (gt_img.shape[1], gt_img.shape[0]), interpolation = cv2.INTER_CUBIC)
        ## resize correctly
        h, w = gt_img.shape[0], gt_img.shape[1]
        th = (gt_img.shape[0]) // 64 * 64
        tw = (gt_img.shape[1]) // 64 * 64
        gt_img = gt_img[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]
        # pdb.set_trace()


        multichannel = True if len(pred_img.shape) == 3 else False
        # pdb.set_trace()


        cur_psnr = skimage.measure.compare_psnr(gt_img, pred_img, data_range=255)
        cur_ssim = skimage.measure.compare_ssim(gt_img, pred_img, data_range=255, multichannel=multichannel)
        mean_psnr += cur_psnr
        mean_ssim += cur_ssim
        print("cur psnr = {}, cur ssim = {}".format(cur_psnr, cur_ssim))

        # print("cur psnr = {}".format(cur_psnr))

    mean_psnr /= len(pred_frames)
    mean_ssim /= len(pred_frames)

    print("PSNR = {} | SSIM = {}".format(mean_psnr, mean_ssim))
    pdb.set_trace()


def compute_metrics_multi_steps(propagate):
    from collections import defaultdict
    # pred_frames = os.listdir(args.target_dir)
    # gt_frames = os.listdir(os.path.expanduser(args.source_dir))
    pred_frames = glob.glob(args.target_dir + "/*.png")
    gt_frames = glob.glob(os.path.expanduser(args.source_dir + "/*.png"))
    pred_frames.sort()
    gt_frames.sort()
    mean_psnr, mean_ssim = 0, 0
    dict_result_psnr_per_timstep = defaultdict(list)

    for i in range(len(pred_frames) - 1):
        print("gt frame : {} , pred_frame : {}".format(gt_frames[i+2], pred_frames[i]))
        # pdb.set_trace()
        # gt_img = cv2.cvtColor(cv2.imread(gt_frames[i + 2]), cv2.COLOR_BGR2GRAY).astype(float)
        # pred_img = cv2.cvtColor(cv2.imread(pred_frames[i]), cv2.COLOR_BGR2GRAY).astype(float)
        ## convert to YUV to compute stats
        gt_img = cv2.cvtColor(cv2.imread(gt_frames[i + 2]), cv2.COLOR_BGR2YUV).astype(float)
        pred_img = cv2.cvtColor(cv2.imread(pred_frames[i]), cv2.COLOR_BGR2YUV).astype(float)


        pred_img = cv2.resize(pred_img, (gt_img.shape[1], gt_img.shape[0]), interpolation = cv2.INTER_CUBIC)        ## resize correctly


        multichannel = True if len(pred_img.shape) == 3 else False
        # pdb.set_trace()


        cur_psnr = skimage.measure.compare_psnr(gt_img, pred_img, data_range=255)
        cur_ssim = skimage.measure.compare_ssim(gt_img, pred_img, data_range=255, multichannel=multichannel)
        mean_psnr += cur_psnr
        mean_ssim += cur_ssim
        print("cur psnr = {}, cur ssim = {}".format(cur_psnr, cur_ssim))

        dict_result_psnr_per_timstep[(i % propagate)].append(cur_psnr)

        # pdb.set_trace()

        # print("cur psnr = {}".format(cur_psnr))

    mean_psnr /= len(pred_frames) - 1
    mean_ssim /= len(pred_frames) - 1

    print("PSNR = {} | SSIM = {}".format(mean_psnr, mean_ssim))

    with open('dict_result_psnr_per_timestep.pkl', 'wb') as f:
        pickle.dump(dict_result_psnr_per_timstep, f)
    print("saved dict result psnr per timestep as a pickle file")
    pdb.set_trace()



def investigate_psnr_per_timestep():
    import matplotlib.pyplot as plt
    with open("dict_result_psnr_per_timestep.pkl", "rb") as f:
        results = pickle.load(f)
        # pdb.set_trace()

    mean_psnr_per_timestep = [0 for i in range(len(results))]
    timesteps = [(i + 1) * 20 for i in range(len(results))]
    for i in range(len(results)):
        mean_psnr_per_timestep[i] = np.array(results[i]).mean()

    plt.plot(timesteps, mean_psnr_per_timestep, marker="o")
    # plt.title("SDCNet extrapolation using Stuttgart sequence of Cityscapes")

    plt.xlabel("time [ms]")
    plt.ylabel("PSNR [dB]")

    plt.savefig("sdcnet_extrapolation.png")
    print("Figure saved!")

    pdb.set_trace()


def investigate_psnr_per_timestep_with_copylast():
    import matplotlib.pyplot as plt
    with open("dict_result_psnr_per_timestep.pkl", "rb") as f:
        results = pickle.load(f)
        # pdb.set_trace()

    mean_psnr_per_timestep = [0 for i in range(len(results))]
    timesteps = [int(i + 1)  for i in range(len(results))]
    for i in range(len(results)):
        mean_psnr_per_timestep[i] = np.array(results[i]).mean()

    plt.plot(timesteps, mean_psnr_per_timestep, marker="o", label="sdc=net")
    copylast_psnr_per_timestep =     [27.564518698928122, 25.381886373322505 , \
                                      24.32881763973486, 23.62860583304692, 23.120549764594514]
    plt.plot(timesteps, copylast_psnr_per_timestep, marker="o", label="copylast")

    # plt.title("SDCNet extrapolation using Stuttgart sequence of Cityscapes")

    plt.xlabel("image pred")
    plt.ylabel("PSNR [dB]")
    plt.ylim(ymin=20)
    plt.legend(loc="upper right")
    plt.xticks([1,2,3,4,5])


    plt.savefig("sdcnet_extrapolation.png")
    print("Figure saved!")

    pdb.set_trace()


if __name__ == "__main__":
    global args
    args = parser.parse_args()

    # Load pre-trained video reconstruction model
    net = get_model()
    net.eval()
    net = net.cuda()

    # Config paths
    if not os.path.exists(args.target_dir):
        os.makedirs(args.target_dir)

    one_step_augmentation(net)
    compute_metrics()

    propagate = 5
    # multi_step_augmentation(net)
    # compute_metrics_multi_steps(propagate)
    # investigate_psnr_per_timestep()


    # investigate_psnr_per_timestep_with_copylast()
