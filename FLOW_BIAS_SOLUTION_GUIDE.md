# Flow Bias Solution Guide

## 🔍 Il Problema Identificato

### **Causa del Problema**
Il modello SDCNet2D originale ha valori hardcoded di `flow_mean` e `flow_std` che causano un bias sistematico:

```python
flow_mean = [-0.94427323, -1.23077035]  # Movimento verso sinistra e alto
flow_std = [13.77204132, 7.47463894]    # Variabilità del movimento
```

### **<PERSON><PERSON><PERSON> Osservato**
- Immagini traslate di ~1 pixel a sinistra e in alto
- Bordi "allungati" per riempire lo spazio vuoto
- Problema ricorrente in tutte le inferenze

---

## 🎯 Soluzioni Disponibili

### **Soluzione 1: Fix Immediato (RACCOMANDATO)**
Ho già corretto il modello `SDCNet2DWithMasks` impostando `flow_mean = [0.0, 0.0]`.

**Usa subito:**
```bash
python train_sdcnet_with_masks_quick_test.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0
```

Il modello ora usa `flow_mean = [0.0, 0.0]` per default, eliminando il bias.

### **Soluzione 2: Calcolo Automatico delle Statistiche**
Calcola i valori ottimali per il tuo dataset specifico:

```bash
python calculate_flow_statistics.py \
  --dataset_path /path/to/your/dataset/train \
  --max_samples 1000 \
  --output_file my_flow_stats.json
```

**Output esempio:**
```json
{
  "recommendations": {
    "flow_mean_x": 0.123456,
    "flow_mean_y": -0.234567,
    "flow_std_x": 8.765432,
    "flow_std_y": 5.432109
  }
}
```

**Poi usa i valori calcolati:**
```bash
python train_sdcnet_with_masks.py \
  --model SDCNet2DWithMasksConfigurable \
  --flow_mean_x 0.123456 \
  --flow_mean_y -0.234567 \
  --flow_std_x 8.765432 \
  --flow_std_y 5.432109
```

### **Soluzione 3: Normalizzazione Adattiva**
Il modello calcola automaticamente le statistiche durante il training:

```bash
python train_sdcnet_with_masks.py \
  --model SDCNet2DWithMasksAdaptive \
  --adaptive_flow_norm True \
  --flow_momentum 0.1
```

---

## 📊 Confronto delle Soluzioni

| Soluzione | Semplicità | Accuratezza | Quando Usare |
|-----------|------------|-------------|--------------|
| **Fix Immediato** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | Dataset con movimenti casuali |
| **Calcolo Automatico** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Dataset con pattern specifici |
| **Adattiva** | ⭐⭐ | ⭐⭐⭐⭐ | Dataset eterogenei |

---

## 🚀 Procedura Raccomandata

### **Passo 1: Test Immediato**
```bash
# Testa subito il fix
python train_sdcnet_with_masks_quick_test.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0
```

Controlla le inferenze salvate - il problema dovrebbe essere risolto.

### **Passo 2: Verifica Visiva (Opzionale)**
```bash
# Confronta modello originale vs corretto
python test_flow_bias_fix.py
```

### **Passo 3: Training Completo**
Se il quick test funziona:
```bash
python train_sdcnet_with_masks.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0 \
  --epochs 100 \
  --batch_size 4 \
  --patience 10
```

---

## 🔧 Spiegazione Tecnica

### **Cosa Sono flow_mean e flow_std**

#### **flow_mean = [x, y]**
- **Significato**: Movimento medio previsto in pixel per frame
- **x < 0**: Movimento verso sinistra
- **x > 0**: Movimento verso destra  
- **y < 0**: Movimento verso l'alto
- **y > 0**: Movimento verso il basso

#### **flow_std = [x, y]**
- **Significato**: Variabilità del movimento
- **Valori alti**: Movimenti molto variabili
- **Valori bassi**: Movimenti più consistenti

### **Come Influenzano il Modello**

1. **Normalizzazione Input**: `(flow - flow_mean) / flow_std`
2. **Denormalizzazione Output**: `flow_std * output + flow_mean`

Se `flow_mean ≠ [0,0]`, viene aggiunto un bias sistematico all'output.

### **Valori Originali Problematici**
```python
flow_mean = [-0.94427323, -1.23077035]  # Da dataset Cityscapes
```
- Movimento medio: 0.94 pixel a sinistra, 1.23 pixel in alto
- Appropriato per video di guida urbana, NON per dataset generici

---

## 🎯 Raccomandazioni per Tipo di Dataset

### **Dataset con Movimenti Casuali**
```python
flow_mean = [0.0, 0.0]     # Nessun bias
flow_std = [1.0, 1.0]      # Normalizzazione neutra
```

### **Dataset con Movimenti Sistematici**
Usa `calculate_flow_statistics.py` per calcolare i valori ottimali.

### **Dataset Misti/Sconosciuti**
Usa `SDCNet2DWithMasksAdaptive` per adattamento automatico.

---

## ✅ Verifica della Soluzione

### **Controlli Visivi**
1. **Inferenze salvate**: Non dovrebbero più essere traslate
2. **Bordi**: Non dovrebbero essere "allungati"
3. **Oggetti statici**: Dovrebbero rimanere nella stessa posizione

### **Controlli Numerici**
```python
# Controlla le statistiche del flow durante training
model.get_flow_statistics()  # Solo per modello Adaptive
```

### **Test di Regressione**
```bash
# Confronta con immagini identiche (nessun movimento)
python test_flow_bias_fix.py
```

---

## 🚨 Troubleshooting

### **Problema Persiste**
1. **Verifica il modello**: Assicurati di usare `SDCNet2DWithMasks` (corretto)
2. **Controlla i checkpoint**: Modelli pre-addestrati potrebbero avere bias
3. **Verifica il dataset**: Problemi nella struttura dati

### **Nuovi Problemi**
1. **Loss troppo alta**: Prova valori di `flow_std` più grandi
2. **Training instabile**: Usa `flow_momentum` più basso (0.01)
3. **Convergenza lenta**: Calcola statistiche specifiche del dataset

---

## 📈 Risultati Attesi

### **Prima del Fix**
- Traslazione sistematica di ~1 pixel
- Bordi distorti
- Oggetti statici che si muovono

### **Dopo il Fix**
- Nessuna traslazione sistematica
- Bordi preservati
- Oggetti statici rimangono fermi
- Miglior qualità visiva generale

---

## 🎉 Conclusione

Il problema è stato **identificato e risolto**. La causa era il bias sistematico nei valori di `flow_mean` hardcoded per un dataset diverso.

**Soluzione immediata**: Usa `SDCNet2DWithMasks` che ha `flow_mean = [0.0, 0.0]`.

**Per ottimizzazione avanzata**: Calcola statistiche specifiche del tuo dataset con `calculate_flow_statistics.py`.

Il training dovrebbe ora produrre risultati visivamente corretti senza traslazioni indesiderate! 🎯
