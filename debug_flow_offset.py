#!/usr/bin/env python3
"""
Debug script to analyze the 30-pixel left shift issue
This script analyzes the flow values and warping to identify the exact cause.
"""

import torch
import numpy as np
import cv2
import os
import sys
from datetime import datetime
import matplotlib.pyplot as plt

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d_with_masks import SDCNet2DWithMasks
import argparse

def create_test_args():
    """Create test arguments"""
    args = argparse.Namespace()

    # Model parameters
    args.sequence_length = 2
    args.rgb_max = 255.0
    args.flownet2_checkpoint = './flownet2_pytorch/FlowNet2_checkpoint.pth.tar'

    # FlowNet2 required parameters
    args.fp16 = False
    args.rgb_max = 255.0

    # Dataset parameters
    args.sample_rate = 1
    args.crop_size = [256, 320]
    args.start_index = 0
    args.stride = 64

    return args

def create_test_pattern():
    """Create a test pattern with known features to track"""
    height, width = 256, 320

    # Create a checkerboard pattern
    pattern = np.zeros((height, width, 3), dtype=np.uint8)

    # Add vertical stripes every 30 pixels to measure shift
    for x in range(0, width, 30):
        pattern[:, x:x+5] = 255  # White stripes

    # Add horizontal stripes every 30 pixels
    for y in range(0, height, 30):
        pattern[y:y+5, :] = [255, 0, 0]  # Red stripes

    # Add corner markers
    pattern[10:20, 10:20] = [0, 255, 0]  # Green top-left
    pattern[10:20, -20:-10] = [0, 0, 255]  # Blue top-right
    pattern[-20:-10, 10:20] = [255, 255, 0]  # Cyan bottom-left
    pattern[-20:-10, -20:-10] = [255, 0, 255]  # Magenta bottom-right

    # Create binary mask - foreground where there are features
    mask = np.zeros((height, width), dtype=np.uint8)
    mask[pattern.sum(axis=2) > 0] = 255

    return pattern, mask

def analyze_flow_values(model, input_dict):
    """Analyze the raw flow values from FlowNet2"""
    model.eval()

    with torch.no_grad():
        # Move to GPU if available
        if torch.cuda.is_available():
            model = model.cuda()
            for key in input_dict:
                input_dict[key] = [tensor.cuda() for tensor in input_dict[key]]

        # Get input images for flow computation
        input_images, input_masks, last_image, target_image = model.prepare_inputs(input_dict)

        # Compute raw optical flow
        raw_flows = model.interframe_optical_flow(input_images)

        # Get the flow through the network
        flow_prediction = model.network_output(input_images, input_masks, raw_flows)

        # Move back to CPU for analysis
        raw_flow = raw_flows[0].cpu().numpy()  # [batch, 2, H, W]
        predicted_flow = flow_prediction.cpu().numpy()

        print("Flow Analysis:")
        print(f"Raw flow shape: {raw_flow.shape}")
        print(f"Predicted flow shape: {predicted_flow.shape}")

        # Analyze raw flow statistics
        raw_flow_x = raw_flow[0, 0]  # X component
        raw_flow_y = raw_flow[0, 1]  # Y component

        print(f"\nRaw Flow Statistics:")
        print(f"  X: mean={raw_flow_x.mean():.6f}, std={raw_flow_x.std():.6f}, min={raw_flow_x.min():.6f}, max={raw_flow_x.max():.6f}")
        print(f"  Y: mean={raw_flow_y.mean():.6f}, std={raw_flow_y.std():.6f}, min={raw_flow_y.min():.6f}, max={raw_flow_y.max():.6f}")

        # Analyze predicted flow statistics
        pred_flow_x = predicted_flow[0, 0]
        pred_flow_y = predicted_flow[0, 1]

        print(f"\nPredicted Flow Statistics:")
        print(f"  X: mean={pred_flow_x.mean():.6f}, std={pred_flow_x.std():.6f}, min={pred_flow_x.min():.6f}, max={pred_flow_x.max():.6f}")
        print(f"  Y: mean={pred_flow_y.mean():.6f}, std={pred_flow_y.std():.6f}, min={pred_flow_y.min():.6f}, max={pred_flow_y.max():.6f}")

        # Check model's flow normalization parameters
        print(f"\nModel Flow Normalization:")
        print(f"  flow_mean: {model.flow_mean.cpu().numpy().flatten()}")
        print(f"  flow_std: {model.flow_std.cpu().numpy().flatten()}")

        return raw_flow, predicted_flow

def test_warping_manually(image, flow):
    """Test warping manually to understand the offset"""
    height, width = image.shape[:2]

    # Create coordinate grids
    y_coords, x_coords = np.mgrid[0:height, 0:width]

    # Apply flow
    new_x = x_coords + flow[0]
    new_y = y_coords + flow[1]

    # Manual bilinear interpolation
    warped = np.zeros_like(image)

    for y in range(height):
        for x in range(width):
            src_x = new_x[y, x]
            src_y = new_y[y, x]

            # Check bounds
            if 0 <= src_x < width-1 and 0 <= src_y < height-1:
                # Bilinear interpolation
                x0, y0 = int(src_x), int(src_y)
                x1, y1 = x0 + 1, y0 + 1

                dx = src_x - x0
                dy = src_y - y0

                if x1 < width and y1 < height:
                    warped[y, x] = (
                        (1-dx) * (1-dy) * image[y0, x0] +
                        dx * (1-dy) * image[y0, x1] +
                        (1-dx) * dy * image[y1, x0] +
                        dx * dy * image[y1, x1]
                    )

    return warped

def analyze_shift_pattern(original, warped, save_dir):
    """Analyze the shift pattern by comparing features"""
    os.makedirs(save_dir, exist_ok=True)

    # Convert to grayscale for analysis
    if len(original.shape) == 3:
        orig_gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
        warp_gray = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)
    else:
        orig_gray = original
        warp_gray = warped

    # Find vertical edges (to measure horizontal shift)
    edges_orig = cv2.Canny(orig_gray, 50, 150)
    edges_warp = cv2.Canny(warp_gray, 50, 150)

    # Sum along vertical axis to get horizontal edge profile
    profile_orig = np.sum(edges_orig, axis=0)
    profile_warp = np.sum(edges_warp, axis=0)

    # Find peaks in profiles
    def find_peaks(profile, min_distance=20):
        peaks = []
        for i in range(min_distance, len(profile) - min_distance):
            if profile[i] > np.max(profile[i-min_distance:i+min_distance]) * 0.8:
                peaks.append(i)
        return peaks

    peaks_orig = find_peaks(profile_orig)
    peaks_warp = find_peaks(profile_warp)

    print(f"\nShift Analysis:")
    print(f"  Original peaks: {peaks_orig[:5]}...")  # Show first 5
    print(f"  Warped peaks: {peaks_warp[:5]}...")

    # Calculate average shift
    if len(peaks_orig) > 0 and len(peaks_warp) > 0:
        # Match peaks and calculate shifts
        shifts = []
        for orig_peak in peaks_orig[:3]:  # Use first 3 peaks
            # Find closest peak in warped
            distances = [abs(warp_peak - orig_peak) for warp_peak in peaks_warp]
            if distances:
                closest_idx = np.argmin(distances)
                shift = peaks_warp[closest_idx] - orig_peak
                shifts.append(shift)

        if shifts:
            avg_shift = np.mean(shifts)
            print(f"  Average horizontal shift: {avg_shift:.2f} pixels")

            if avg_shift < -25:
                print(f"  ⚠️  Significant LEFT shift detected!")
            elif avg_shift > 25:
                print(f"  ⚠️  Significant RIGHT shift detected!")
            else:
                print(f"  ✅ No significant horizontal shift")

    # Save analysis plots
    plt.figure(figsize=(15, 10))

    plt.subplot(2, 3, 1)
    plt.imshow(original)
    plt.title('Original')
    plt.axis('off')

    plt.subplot(2, 3, 2)
    plt.imshow(warped)
    plt.title('Warped')
    plt.axis('off')

    plt.subplot(2, 3, 3)
    diff = np.abs(original.astype(float) - warped.astype(float))
    plt.imshow(diff)
    plt.title('Absolute Difference')
    plt.axis('off')

    plt.subplot(2, 3, 4)
    plt.imshow(edges_orig, cmap='gray')
    plt.title('Original Edges')
    plt.axis('off')

    plt.subplot(2, 3, 5)
    plt.imshow(edges_warp, cmap='gray')
    plt.title('Warped Edges')
    plt.axis('off')

    plt.subplot(2, 3, 6)
    plt.plot(profile_orig, label='Original', alpha=0.7)
    plt.plot(profile_warp, label='Warped', alpha=0.7)
    plt.scatter(peaks_orig, profile_orig[peaks_orig], color='red', s=50, label='Orig Peaks')
    plt.scatter(peaks_warp, profile_warp[peaks_warp], color='blue', s=50, label='Warp Peaks')
    plt.title('Horizontal Edge Profiles')
    plt.xlabel('X Position')
    plt.ylabel('Edge Intensity')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'shift_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    print(f"  Analysis plots saved to: {save_dir}/shift_analysis.png")

def main():
    print("="*60)
    print("Flow Offset Debug Analysis")
    print("="*60)

    # Check if FlowNet2 checkpoint exists
    args = create_test_args()
    if not os.path.exists(args.flownet2_checkpoint):
        print(f"❌ FlowNet2 checkpoint not found at: {args.flownet2_checkpoint}")
        return

    print("✅ FlowNet2 checkpoint found")

    # Create test pattern
    print("\n📝 Creating test pattern...")
    test_image, test_mask = create_test_pattern()
    print("✅ Test pattern created with vertical stripes every 30px")

    # Create input dict (identical frames - should produce zero flow)
    input_dict = {
        'image': [
            torch.from_numpy(test_image.transpose(2, 0, 1)).float().unsqueeze(0),  # t-1
            torch.from_numpy(test_image.transpose(2, 0, 1)).float().unsqueeze(0),  # t
            torch.from_numpy(test_image.transpose(2, 0, 1)).float().unsqueeze(0),  # t+1 (target)
        ],
        'mask': [
            torch.from_numpy(test_mask).float().unsqueeze(0).unsqueeze(0),  # t-1
            torch.from_numpy(test_mask).float().unsqueeze(0).unsqueeze(0),  # t
            torch.from_numpy(test_mask).float().unsqueeze(0).unsqueeze(0),  # t+1
        ]
    }

    # Create model
    print("\n🔍 Creating model...")
    model = SDCNet2DWithMasks(args)
    print("✅ Model created")

    # Analyze flow values
    print("\n📊 Analyzing flow values...")
    raw_flow, predicted_flow = analyze_flow_values(model, input_dict)

    # Test model prediction
    print("\n🔮 Testing model prediction...")
    model.eval()
    with torch.no_grad():
        if torch.cuda.is_available():
            model = model.cuda()
            for key in input_dict:
                input_dict[key] = [tensor.cuda() for tensor in input_dict[key]]

        losses, prediction, target = model(input_dict)

        # Move back to CPU
        prediction = prediction.cpu().numpy()[0].transpose(1, 2, 0)
        target = target.cpu().numpy()[0].transpose(1, 2, 0)

        prediction = np.clip(prediction, 0, 255).astype(np.uint8)
        target = np.clip(target, 0, 255).astype(np.uint8)

    # Analyze the shift
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f"flow_debug_{timestamp}"

    print(f"\n🔍 Analyzing shift pattern...")
    analyze_shift_pattern(target, prediction, save_dir)

    # Save flow visualizations
    flow_x = predicted_flow[0, 0]
    flow_y = predicted_flow[0, 1]

    # Create flow visualization
    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.imshow(flow_x, cmap='RdBu', vmin=-5, vmax=5)
    plt.colorbar(label='Flow X (pixels)')
    plt.title(f'Flow X\nMean: {flow_x.mean():.3f}')

    plt.subplot(1, 3, 2)
    plt.imshow(flow_y, cmap='RdBu', vmin=-5, vmax=5)
    plt.colorbar(label='Flow Y (pixels)')
    plt.title(f'Flow Y\nMean: {flow_y.mean():.3f}')

    plt.subplot(1, 3, 3)
    flow_magnitude = np.sqrt(flow_x**2 + flow_y**2)
    plt.imshow(flow_magnitude, cmap='hot')
    plt.colorbar(label='Flow Magnitude')
    plt.title(f'Flow Magnitude\nMean: {flow_magnitude.mean():.3f}')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'flow_visualization.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # Save images
    cv2.imwrite(os.path.join(save_dir, 'test_pattern.png'), test_image)
    cv2.imwrite(os.path.join(save_dir, 'prediction.png'), prediction)
    cv2.imwrite(os.path.join(save_dir, 'target.png'), target)

    print(f"\n📁 All results saved to: {save_dir}")

    # Summary
    print(f"\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print(f"Flow X mean: {flow_x.mean():.6f} pixels")
    print(f"Flow Y mean: {flow_y.mean():.6f} pixels")

    if abs(flow_x.mean()) > 1.0:
        print(f"⚠️  Significant flow bias detected in X direction!")
        print(f"   This could explain the 30-pixel left shift.")

    if abs(flow_y.mean()) > 1.0:
        print(f"⚠️  Significant flow bias detected in Y direction!")

    print(f"\nNext steps:")
    print(f"1. Check the flow visualization in {save_dir}")
    print(f"2. If flow bias is confirmed, adjust flow_std scaling factor")
    print(f"3. Consider using SDCNet2DWithMasksConfigurable with custom parameters")

if __name__ == "__main__":
    main()
