{"model": "SDCNet2DWithMasks", "dataset": "FrameLoaderWithMasks", "sequence_length": 2, "rgb_max": 255.0, "flownet2_checkpoint": "./flownet2_pytorch/FlowNet2_checkpoint.pth.tar", "epochs": 100, "batch_size": 4, "val_batch_size": 2, "lr": 0.0001, "weight_decay": 0.0001, "workers": 4, "train_file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\tesi-video-segmentati\\sdcnet\\dataset\\train", "val_file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\tesi-video-segmentati\\sdcnet\\dataset\\val", "sample_rate": 1, "crop_size": [256, 320], "start_index": 0, "stride": 64, "save_dir": "./checkpoints", "name": "sdcnet_with_masks", "resume": "./checkpoints/masks_1_epoch_039.pth.pth", "patience": 10, "min_delta": 0.001, "gpu": 0, "fp16": false, "save_freq": 5, "inference_batches": [1, 1000, 2000], "inference_samples": 3, "keep_best_models": 3, "keep_regular_checkpoints": 2}