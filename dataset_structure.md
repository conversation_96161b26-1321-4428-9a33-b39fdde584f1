# Example Dataset Structure

This file shows exactly how your dataset should look when properly organized.

## 📁 Complete Structure Example

```
dataset/
├── train/
│   ├── X/                                    # Input data
│   │   ├── kitchen_scene_01/                 # Video 1 frames
│   │   │   ├── 000001.png                    # Frame 1
│   │   │   ├── 000002.png                    # Frame 2
│   │   │   ├── 000003.png                    # Frame 3
│   │   │   ├── 000004.png                    # Frame 4
│   │   │   ├── 000005.png                    # Frame 5
│   │   │   └── ...                           # More frames
│   │   ├── kitchen_scene_01_binary_ground_truth/  # Video 1 masks
│   │   │   ├── 000001.png                    # Mask 1 (binary: 0/255)
│   │   │   ├── 000002.png                    # Mask 2 (binary: 0/255)
│   │   │   ├── 000003.png                    # Mask 3 (binary: 0/255)
│   │   │   ├── 000004.png                    # Mask 4 (binary: 0/255)
│   │   │   ├── 000005.png                    # Mask 5 (binary: 0/255)
│   │   │   └── ...                           # More masks
│   │   ├── kitchen_scene_02/                 # Video 1 frames
│   │   │   ├── 000001.png                    # Frame 1
│   │   │   ├── 000002.png                    # Frame 2
│   │   │   ├── 000003.png                    # Frame 3
│   │   │   ├── 000004.png                    # Frame 4
│   │   │   ├── 000005.png                    # Frame 5
│   │   │   └── ...                           # More frames
│   │   ├── kitchen_scene_02_binary_ground_truth/  # Video 1 masks
│   │   │   ├── 000001.png                    # Mask 1 (binary: 0/255)
│   │   │   ├── 000002.png                    # Mask 2 (binary: 0/255)
│   │   │   ├── 000003.png                    # Mask 3 (binary: 0/255)
│   │   │   ├── 000004.png                    # Mask 4 (binary: 0/255)
│   │   │   ├── 000005.png                    # Mask 5 (binary: 0/255)
│   │   │   └── ...                           # More masks
│   │   ├── person_walking_02/                # Video 2 frames
│   │   │   ├── 000001.png
│   │   │   ├── 000002.png
│   │   │   └── ...
│   │   ├── person_walking_02_binary_ground_truth/  # Video 2 masks
│   │   │   ├── 000001.png
│   │   │   ├── 000002.png
│   │   │   └── ...
│   │   ├── car_driving_03/                   # Video 3 frames
│   │   ├── car_driving_03_binary_ground_truth/     # Video 3 masks
│   │   └── ...                               # More videos
│   └── Y/                                    # Target data
│       ├── kitchen_scene_01/                 # Video 1 targets
│       │   ├── 000001.png                    # Target frame 1
│       │   ├── 000002.png                    # Target frame 2
│       │   ├── 000003.png                    # Target frame 3
│       │   ├── 000004.png                    # Target frame 4
│       │   ├── 000005.png                    # Target frame 5
│       │   └── ...                           # More targets
│       ├── kitchen_scene_02/                 # Video 1 targets
│       │   ├── 000001.png                    # Target frame 1
│       │   ├── 000002.png                    # Target frame 2
│       │   ├── 000003.png                    # Target frame 3
│       │   ├── 000004.png                    # Target frame 4
│       │   ├── 000005.png                    # Target frame 5
│       │   └── ...                           # More targets
│       ├── person_walking_02/                # Video 2 targets
│       ├── car_driving_03/                   # Video 3 targets
│       └── ...                               # More videos
├── val/                                      # Validation split
│   ├── X/
│   │   ├── val_kitchen_scene_04/
│   │   ├── val_kitchen_scene_04_binary_ground_truth/
│   │   ├── val_person_walking_05/
│   │   ├── val_person_walking_05_binary_ground_truth/
│   │   └── ...
│   └── Y/
│       ├── val_kitchen_scene_04/
│       ├── val_person_walking_03/
│       └── ...
└── test/                                     # Test split
    ├── X/
    │   ├── test_kitchen_scene_06/
    │   ├── test_kitchen_scene_06_binary_ground_truth/
    │   ├── test_person_walking_04/
    │   ├── test_person_walking_07_binary_ground_truth/
    │   └── ...
    └── Y/
        ├── test_kitchen_scene_06/
        ├── test_person_walking_04/
        └── ...
```

## 🎯 Key Points

### 1. Naming Convention
- **Video directories**: Any descriptive name (e.g., `kitchen_scene_01`)
- **Particular names**: Some names may be integers (e.g, `1`)
- **Mask directories**: Video name + `_binary_ground_truth` (e.g., `kitchen_scene_01_binary_ground_truth`)
- **Frame files**: Sequential numbering with leading zeros (e.g., `000001.png`)

### 2. File Correspondence
- **Identical filenames**: Frame and mask files must have exactly the same names
- **Same count**: Each video directory must have the same number of files as its mask directory
- **Same format**: All files should use the same image format (PNG recommended)

### 3. Data Requirements
- **Binary masks**: Mask values must be exactly 0 (background) and 255 (object)
- **Consistent resolution**: All frames in a video should have the same resolution

### 4. Directory Structure
- **X directories**: Contain both frames and masks for input
- **Y directories**: Contain target frames only
- **Three splits**: train, val, test with identical internal structure