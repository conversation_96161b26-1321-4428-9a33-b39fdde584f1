python train_sdcnet_with_masks_quick_test.py --train_file C:\Users\<USER>\OneDrive\Desktop\tesi-video-segmentati\sdcnet\dataset\train   --val_file C:\Users\<USER>\OneDrive\Desktop\tesi-video-segmentati\sdcnet\dataset\val   --gpu 0




python train_sdcnet_with_masks.py  --train_file C:\Users\<USER>\OneDrive\Desktop\tesi-video-segmentati\sdcnet\dataset\train   --val_file C:\Users\<USER>\OneDrive\Desktop\tesi-video-segmentati\sdcnet\dataset\val   --gpu 0   --epochs 1000   --batch_size 4   --patience 10   --name masks_1