#!/usr/bin/env python3
"""
Calculate flow statistics from your dataset
This script computes the optimal flow_mean and flow_std values for your specific dataset.
"""

import os
import sys
import argparse
import torch
import numpy as np
from tqdm import tqdm
import cv2
import json
from datetime import datetime

# Add current directory to path
sys.path.append('.')

from datasets.frame_loader_with_masks import FrameLoaderWithMasks
from flownet2_pytorch.models import FlowNet2

def create_args():
    parser = argparse.ArgumentParser(description='Calculate Flow Statistics for Dataset')
    
    # Model parameters (for FlowNet2)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint', 
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)
    
    # Dataset parameters
    parser.add_argument('--dataset_path', required=True, type=str,
                       help='Path to dataset (train or val directory)')
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)
    
    # Calculation parameters
    parser.add_argument('--max_samples', default=1000, type=int,
                       help='Maximum number of samples to analyze (0 = all)')
    parser.add_argument('--batch_size', default=4, type=int)
    parser.add_argument('--workers', default=4, type=int)
    
    # Output
    parser.add_argument('--output_file', default='flow_statistics.json', type=str)
    parser.add_argument('--gpu', default=0, type=int)
    
    return parser.parse_args()

def setup_gpu(gpu_id):
    """Setup GPU device"""
    if torch.cuda.is_available():
        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
        torch.cuda.set_device(0)
        print(f"Using GPU {gpu_id}: {torch.cuda.get_device_name(0)}")
        return True
    else:
        print("CUDA not available, using CPU")
        return False

def compute_optical_flow(flownet2, input_images):
    """Compute optical flow between consecutive frames"""
    # Prepare input for FlowNet2
    flownet2_inputs = torch.stack(
        [torch.cat([input_images[i + 1].unsqueeze(2), input_images[i].unsqueeze(2)], dim=2) 
         for i in range(len(input_images) - 1)], dim=0).contiguous()

    batch_size, channel_count, height, width = input_images[0].shape
    flownet2_inputs_flattened = flownet2_inputs.view(-1, channel_count, 2, height, width)
    
    flows = []
    for flownet2_input in torch.chunk(flownet2_inputs_flattened, len(input_images) - 1):
        flow = flownet2(flownet2_input)
        flows.append(flow)
    
    return flows

def analyze_dataset_flows(args, device):
    """Analyze flows in the dataset to compute statistics"""
    
    # Create FlowNet2 model
    flownet2 = FlowNet2(args, batchNorm=False)
    
    if not os.path.exists(args.flownet2_checkpoint):
        raise FileNotFoundError(f"FlowNet2 checkpoint not found: {args.flownet2_checkpoint}")
    
    checkpoint = torch.load(args.flownet2_checkpoint, map_location=device)
    flownet2.load_state_dict(checkpoint['state_dict'], strict=False)
    flownet2 = flownet2.to(device)
    flownet2.eval()
    
    # Freeze FlowNet2 parameters
    for param in flownet2.parameters():
        param.requires_grad = False
    
    print("✅ FlowNet2 loaded successfully")
    
    # Create dataset
    dataset = FrameLoaderWithMasks(args, args.dataset_path, is_training=False)
    
    # Limit samples if requested
    if args.max_samples > 0 and len(dataset) > args.max_samples:
        indices = torch.randperm(len(dataset))[:args.max_samples]
        dataset = torch.utils.data.Subset(dataset, indices)
    
    dataloader = torch.utils.data.DataLoader(
        dataset, batch_size=args.batch_size, shuffle=False, 
        num_workers=args.workers, pin_memory=True
    )
    
    print(f"📊 Analyzing {len(dataset)} samples...")
    
    # Collect all flows
    all_flows = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="Computing flows")):
            # Get input images (exclude target)
            input_images = [img.to(device) for img in batch['image'][:-1]]
            
            # Compute optical flows
            flows = compute_optical_flow(flownet2, input_images)
            
            # Collect flows
            for flow in flows:
                # Convert to numpy and flatten spatial dimensions
                flow_np = flow.cpu().numpy()  # Shape: [batch, 2, H, W]
                
                # Reshape to [batch*H*W, 2] for statistics
                batch_size, channels, height, width = flow_np.shape
                flow_flat = flow_np.transpose(0, 2, 3, 1).reshape(-1, 2)  # [batch*H*W, 2]
                
                all_flows.append(flow_flat)
    
    # Concatenate all flows
    all_flows = np.concatenate(all_flows, axis=0)  # [total_pixels, 2]
    
    print(f"📈 Collected {all_flows.shape[0]:,} flow vectors")
    
    return all_flows

def compute_statistics(flows):
    """Compute mean and std statistics from flows"""
    
    # Basic statistics
    flow_mean = np.mean(flows, axis=0)
    flow_std = np.std(flows, axis=0)
    flow_median = np.median(flows, axis=0)
    
    # Robust statistics (less sensitive to outliers)
    flow_percentiles = np.percentile(flows, [25, 75], axis=0)
    flow_iqr = flow_percentiles[1] - flow_percentiles[0]
    
    # Remove outliers for robust statistics
    q1 = np.percentile(flows, 25, axis=0)
    q3 = np.percentile(flows, 75, axis=0)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    
    # Mask for non-outliers
    mask = np.all((flows >= lower_bound) & (flows <= upper_bound), axis=1)
    flows_no_outliers = flows[mask]
    
    flow_mean_robust = np.mean(flows_no_outliers, axis=0)
    flow_std_robust = np.std(flows_no_outliers, axis=0)
    
    print(f"\n📊 Flow Statistics:")
    print(f"  Total flow vectors: {flows.shape[0]:,}")
    print(f"  After outlier removal: {flows_no_outliers.shape[0]:,} ({100*len(flows_no_outliers)/len(flows):.1f}%)")
    print(f"\n  Basic Statistics:")
    print(f"    Mean: [{flow_mean[0]:+.6f}, {flow_mean[1]:+.6f}]")
    print(f"    Std:  [{flow_std[0]:+.6f}, {flow_std[1]:+.6f}]")
    print(f"    Median: [{flow_median[0]:+.6f}, {flow_median[1]:+.6f}]")
    print(f"\n  Robust Statistics (recommended):")
    print(f"    Mean: [{flow_mean_robust[0]:+.6f}, {flow_mean_robust[1]:+.6f}]")
    print(f"    Std:  [{flow_std_robust[0]:+.6f}, {flow_std_robust[1]:+.6f}]")
    
    # Movement analysis
    print(f"\n🎯 Movement Analysis:")
    if abs(flow_mean_robust[0]) > 0.5:
        direction_x = "left" if flow_mean_robust[0] < 0 else "right"
        print(f"    Systematic horizontal movement: {abs(flow_mean_robust[0]):.2f} pixels {direction_x}")
    else:
        print(f"    No significant horizontal bias")
        
    if abs(flow_mean_robust[1]) > 0.5:
        direction_y = "up" if flow_mean_robust[1] < 0 else "down"
        print(f"    Systematic vertical movement: {abs(flow_mean_robust[1]):.2f} pixels {direction_y}")
    else:
        print(f"    No significant vertical bias")
    
    return {
        'basic': {
            'mean': flow_mean.tolist(),
            'std': flow_std.tolist(),
            'median': flow_median.tolist()
        },
        'robust': {
            'mean': flow_mean_robust.tolist(),
            'std': flow_std_robust.tolist()
        },
        'metadata': {
            'total_vectors': int(flows.shape[0]),
            'vectors_after_outlier_removal': int(flows_no_outliers.shape[0]),
            'outlier_percentage': float(100 * (1 - len(flows_no_outliers)/len(flows)))
        }
    }

def save_results(statistics, args):
    """Save results to file"""
    
    # Add metadata
    result = {
        'dataset_path': args.dataset_path,
        'timestamp': datetime.now().isoformat(),
        'parameters': {
            'max_samples': args.max_samples,
            'sequence_length': args.sequence_length,
            'crop_size': args.crop_size
        },
        'statistics': statistics,
        'recommendations': {
            'flow_mean_x': statistics['robust']['mean'][0],
            'flow_mean_y': statistics['robust']['mean'][1],
            'flow_std_x': statistics['robust']['std'][0],
            'flow_std_y': statistics['robust']['std'][1]
        }
    }
    
    # Save to file
    with open(args.output_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n💾 Results saved to: {args.output_file}")
    
    return result

def print_usage_instructions(result):
    """Print instructions for using the computed statistics"""
    
    rec = result['recommendations']
    
    print(f"\n" + "="*60)
    print("USAGE INSTRUCTIONS")
    print("="*60)
    
    print(f"\n1. Use with SDCNet2DWithMasksConfigurable:")
    print(f"   python train_sdcnet_with_masks.py \\")
    print(f"     --model SDCNet2DWithMasksConfigurable \\")
    print(f"     --flow_mean_x {rec['flow_mean_x']:.6f} \\")
    print(f"     --flow_mean_y {rec['flow_mean_y']:.6f} \\")
    print(f"     --flow_std_x {rec['flow_std_x']:.6f} \\")
    print(f"     --flow_std_y {rec['flow_std_y']:.6f} \\")
    print(f"     [other parameters...]")
    
    print(f"\n2. Or modify the default values in your model:")
    print(f"   flow_mean = [{rec['flow_mean_x']:.6f}, {rec['flow_mean_y']:.6f}]")
    print(f"   flow_std = [{rec['flow_std_x']:.6f}, {rec['flow_std_y']:.6f}]")
    
    print(f"\n3. For zero bias (recommended if movements are random):")
    print(f"   --flow_mean_x 0.0 --flow_mean_y 0.0")

def main():
    args = create_args()
    
    print("="*60)
    print("Flow Statistics Calculator")
    print("="*60)
    print(f"Dataset: {args.dataset_path}")
    print(f"Max samples: {args.max_samples if args.max_samples > 0 else 'All'}")
    print(f"Output: {args.output_file}")
    
    # Setup GPU
    use_cuda = setup_gpu(args.gpu)
    device = torch.device('cuda' if use_cuda else 'cpu')
    
    try:
        # Analyze flows
        flows = analyze_dataset_flows(args, device)
        
        # Compute statistics
        statistics = compute_statistics(flows)
        
        # Save results
        result = save_results(statistics, args)
        
        # Print usage instructions
        print_usage_instructions(result)
        
        print(f"\n✅ Flow statistics calculation completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
