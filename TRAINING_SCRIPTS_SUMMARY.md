# Training Scripts Summary

## 🎯 Scripts Created

### 1. **`train_sdcnet_with_masks.py`** - Full Training Script
**Purpose**: Complete training pipeline for SDCNet2DWithMasks

**Features**:
- ✅ **GPU Support**: Automatic CUDA setup and device management
- ✅ **tqdm Progress Bars**: Real-time training progress visualization
- ✅ **Early Stopping**: Patience-based stopping with best model restoration
- ✅ **Visual Monitoring**: Inference samples saved at batches 1, 1000, 2000
- ✅ **Comprehensive Logging**: All loss statistics after each epoch
- ✅ **Checkpointing**: Regular saves + best model tracking
- ✅ **Resume Capability**: Continue training from any checkpoint

**Usage**:
```bash
python train_sdcnet_with_masks.py \
  --train_file /path/to/dataset/train \
  --val_file /path/to/dataset/val \
  --gpu 0 \
  --epochs 100 \
  --batch_size 4 \
  --patience 10
```

### 2. **`train_sdcnet_with_masks_quick_test.py`** - Quick Test Script
**Purpose**: Fast validation of training pipeline (2 epochs, limited data)

**Features**:
- ✅ **Quick Validation**: Only 2 epochs for rapid testing
- ✅ **Limited Dataset**: Max 20 train + 10 val samples
- ✅ **Reduced Batches**: Max 10 batches per epoch
- ✅ **Same Features**: All monitoring features of full script
- ✅ **Success Confirmation**: Clear indication when test passes

**Usage**:
```bash
python train_sdcnet_with_masks_quick_test.py \
  --train_file /path/to/dataset/train \
  --val_file /path/to/dataset/val \
  --gpu 0
```

### 3. **`training_examples.md`** - Usage Documentation
**Purpose**: Comprehensive guide for using both training scripts

**Contents**:
- Command examples for different scenarios
- Parameter guidelines and optimization tips
- Troubleshooting common issues
- Expected training progress and timelines

---

## 🚀 Quick Start Workflow

### Step 1: Quick Test (REQUIRED)
```bash
# Test everything works with minimal resources
python train_sdcnet_with_masks_quick_test.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0
```

**Expected Output**:
```
SDCNet2DWithMasks QUICK TEST Training
============================================================
⚠️  THIS IS A QUICK TEST VERSION
Device: cuda:0
Using GPU 0: NVIDIA GeForce RTX 3080
Total parameters: 39,175,123

Epoch 1/2 (Quick Test): 100%|██████████| 10/10 [00:45<00:00]
Validation 1 (Quick Test): 100%|██████████| 5/5 [00:15<00:00]

QUICK TEST COMPLETED SUCCESSFULLY! ✅
```

### Step 2: Full Training (After Quick Test Passes)
```bash
# Run full training with your complete dataset
python train_sdcnet_with_masks.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0 \
  --epochs 100 \
  --batch_size 4 \
  --patience 10 \
  --name my_experiment
```

---

## 📊 Key Features Implemented

### 1. **GPU Management**
```python
def setup_gpu(gpu_id):
    if torch.cuda.is_available():
        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
        torch.cuda.set_device(0)
        print(f"Using GPU {gpu_id}: {torch.cuda.get_device_name(0)}")
```

### 2. **Progress Monitoring with tqdm**
```python
pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{args.epochs}')
pbar.set_postfix({
    'Loss': f"{total_loss.item():.4f}",
    'Color': f"{losses['color'].item():.4f}",
    'Grad': f"{losses['color_gradient'].item():.4f}",
    'Smooth': f"{losses['flow_smoothness'].item():.4f}"
})
```

### 3. **Early Stopping**
```python
class EarlyStopping:
    def __init__(self, patience=10, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_loss = float('inf')
        self.counter = 0
```

### 4. **Visual Inference Monitoring**
```python
def save_inference_samples(model, dataloader, epoch, batch_idx, save_dir, num_samples=3):
    # Saves prediction vs target comparisons
    # Includes input frames and masks for reference
    # Saves loss information as JSON
```

### 5. **Comprehensive Statistics**
After each epoch:
```
Epoch 25 Results:
Train - Total: 0.4234, Color: 0.3123, Gradient: 0.0856, Smoothness: 0.0255
Val   - Total: 0.3891, Color: 0.2834, Gradient: 0.0798, Smoothness: 0.0259
```

---

## 📁 Output Structure

### Training Outputs
```
checkpoints/
├── training_config.json                    # All training parameters
├── sdcnet_with_masks_epoch_005.pth        # Regular checkpoints
├── sdcnet_with_masks_best.pth             # Best validation model
└── training_inference_2024_01_15_14_30_25/
    ├── epoch_000/
    │   ├── batch_0001/                     # Batch 1 inference
    │   │   ├── sample_0000/
    │   │   │   ├── prediction.png          # Model prediction
    │   │   │   ├── target.png              # Ground truth
    │   │   │   ├── input_frame_0.png       # Input frame t-1
    │   │   │   ├── input_frame_1.png       # Input frame t
    │   │   │   ├── mask_0.png              # Mask t-1
    │   │   │   ├── mask_1.png              # Mask t
    │   │   │   ├── mask_2.png              # Mask t+1
    │   │   │   └── losses.json             # Loss breakdown
    │   ├── batch_1000/                     # Batch 1000 inference
    │   └── batch_2000/                     # Batch 2000 inference
    └── epoch_001/
```

---

## 🔧 Configuration Options

### Memory Optimization
| Parameter | Small GPU (8GB) | Medium GPU (12GB) | Large GPU (16GB+) |
|-----------|-----------------|-------------------|-------------------|
| `--batch_size` | 2-4 | 4-6 | 6-8 |
| `--val_batch_size` | 1-2 | 2-3 | 3-4 |
| `--workers` | 2-4 | 4-6 | 6-8 |

### Training Control
| Parameter | Description | Default | Recommended |
|-----------|-------------|---------|-------------|
| `--epochs` | Total training epochs | 100 | 50-200 |
| `--lr` | Learning rate | 0.0001 | 0.0001-0.00001 |
| `--patience` | Early stopping patience | 10 | 10-15 |
| `--save_freq` | Checkpoint frequency | 5 | 5-10 |

### Monitoring
| Parameter | Description | Default |
|-----------|-------------|---------|
| `--inference_batches` | When to save samples | [1, 1000, 2000] |
| `--inference_samples` | Samples per batch | 3 |

---

## ✅ Validation Checklist

Before running full training:

### Prerequisites
- [ ] FlowNet2 checkpoint at `./flownet2_pytorch/FlowNet2_checkpoint.pth.tar`
- [ ] Dataset organized according to `dataset_structure.md`
- [ ] GPU with sufficient memory available
- [ ] Python environment with all dependencies

### Quick Test Validation
- [ ] Quick test script runs without errors
- [ ] GPU is detected and used correctly
- [ ] Model loads and initializes properly
- [ ] Forward and backward passes work
- [ ] Inference samples are saved correctly
- [ ] Loss values are reasonable (not NaN/inf)

### Ready for Full Training
- [ ] Quick test completed successfully
- [ ] Dataset paths are correct
- [ ] Batch size fits in GPU memory
- [ ] Save directory has sufficient space
- [ ] Training parameters are configured

---

## 🚨 Common Issues & Solutions

### 1. **CUDA Out of Memory**
```bash
# Solution: Reduce batch size
--batch_size 2 --val_batch_size 1 --workers 2
```

### 2. **Dataset Loading Errors**
```bash
# Solution: Verify dataset structure
python test_sdcnet_with_masks.py
```

### 3. **FlowNet2 Checkpoint Missing**
```
Error: assert os.path.exists(args.flownet2_checkpoint)
```
**Solution**: Download FlowNet2 checkpoint and place at correct path

### 4. **Slow Training**
```bash
# Solutions:
--workers 2          # Reduce if I/O bound
--crop_size 224 288  # Smaller images
--fp16               # Mixed precision (if supported)
```

---

## 📈 Expected Performance

### Training Progress
- **Initial Loss**: ~1.0-2.0
- **After 10 epochs**: ~0.5-0.8  
- **Converged**: ~0.2-0.4
- **Early stopping**: Usually 50-100 epochs

### Hardware Requirements
- **Minimum**: GTX 1080 (8GB), 16GB RAM
- **Recommended**: RTX 3080+ (12GB+), 32GB RAM
- **Storage**: SSD recommended for dataset

---

## 🎯 Next Steps

1. **Run Quick Test**: Verify everything works
2. **Adjust Parameters**: Based on your hardware and dataset
3. **Start Full Training**: Monitor progress and adjust as needed
4. **Evaluate Results**: Check inference samples and loss curves

The training scripts are ready to use! 🚀
