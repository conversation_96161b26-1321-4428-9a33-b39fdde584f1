#!/usr/bin/env python3
"""
Cleanup old checkpoints to free disk space
Keeps only the most recent N checkpoints and the best model.
"""

import os
import glob
import argparse
from datetime import datetime

def cleanup_checkpoints(checkpoint_dir, keep_last=5, dry_run=False):
    """
    Clean up old checkpoints, keeping only the most recent ones and the best model.
    
    Args:
        checkpoint_dir: Directory containing checkpoints
        keep_last: Number of recent checkpoints to keep
        dry_run: If True, only show what would be deleted
    """
    
    if not os.path.exists(checkpoint_dir):
        print(f"❌ Checkpoint directory not found: {checkpoint_dir}")
        return
    
    # Find all checkpoint files
    checkpoint_pattern = os.path.join(checkpoint_dir, "*_epoch_*.pth")
    checkpoint_files = glob.glob(checkpoint_pattern)
    
    # Find best model files
    best_pattern = os.path.join(checkpoint_dir, "*_best.pth")
    best_files = glob.glob(best_pattern)
    
    if not checkpoint_files:
        print(f"✅ No checkpoint files found in {checkpoint_dir}")
        return
    
    print(f"📁 Found {len(checkpoint_files)} checkpoint files")
    print(f"🏆 Found {len(best_files)} best model files")
    
    # Sort checkpoint files by modification time (newest first)
    checkpoint_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    # Files to keep
    files_to_keep = set()
    
    # Keep best models
    files_to_keep.update(best_files)
    
    # Keep most recent checkpoints
    files_to_keep.update(checkpoint_files[:keep_last])
    
    # Files to delete
    files_to_delete = [f for f in checkpoint_files if f not in files_to_keep]
    
    if not files_to_delete:
        print("✅ No files to delete")
        return
    
    print(f"\n📋 Files to keep ({len(files_to_keep)}):")
    for f in sorted(files_to_keep):
        size_mb = os.path.getsize(f) / (1024 * 1024)
        print(f"  ✅ {os.path.basename(f)} ({size_mb:.1f} MB)")
    
    print(f"\n🗑️  Files to delete ({len(files_to_delete)}):")
    total_size_mb = 0
    for f in sorted(files_to_delete):
        size_mb = os.path.getsize(f) / (1024 * 1024)
        total_size_mb += size_mb
        print(f"  ❌ {os.path.basename(f)} ({size_mb:.1f} MB)")
    
    print(f"\n💾 Total space to free: {total_size_mb:.1f} MB")
    
    if dry_run:
        print("\n🔍 DRY RUN - No files were actually deleted")
        return
    
    # Confirm deletion
    response = input(f"\n❓ Delete {len(files_to_delete)} files to free {total_size_mb:.1f} MB? (y/N): ")
    if response.lower() != 'y':
        print("❌ Deletion cancelled")
        return
    
    # Delete files
    deleted_count = 0
    for f in files_to_delete:
        try:
            os.remove(f)
            deleted_count += 1
            print(f"  🗑️  Deleted: {os.path.basename(f)}")
        except Exception as e:
            print(f"  ❌ Failed to delete {os.path.basename(f)}: {e}")
    
    print(f"\n✅ Successfully deleted {deleted_count}/{len(files_to_delete)} files")
    print(f"💾 Freed approximately {total_size_mb:.1f} MB")

def cleanup_inference_samples(checkpoint_dir, keep_last_epochs=3, dry_run=False):
    """
    Clean up old inference sample directories.
    """
    
    # Find inference directories
    inference_pattern = os.path.join(checkpoint_dir, "training_inference_*")
    inference_dirs = glob.glob(inference_pattern)
    
    if not inference_dirs:
        print("✅ No inference directories found")
        return
    
    print(f"\n📸 Found {len(inference_dirs)} inference directories")
    
    for inference_dir in inference_dirs:
        if not os.path.isdir(inference_dir):
            continue
            
        # Find epoch directories
        epoch_pattern = os.path.join(inference_dir, "epoch_*")
        epoch_dirs = glob.glob(epoch_pattern)
        
        if not epoch_dirs:
            continue
        
        # Sort by epoch number (newest first)
        epoch_dirs.sort(key=lambda x: int(os.path.basename(x).split('_')[1]), reverse=True)
        
        # Keep only recent epochs
        dirs_to_delete = epoch_dirs[keep_last_epochs:]
        
        if not dirs_to_delete:
            continue
        
        print(f"\n📁 {os.path.basename(inference_dir)}:")
        print(f"  ✅ Keeping {min(len(epoch_dirs), keep_last_epochs)} recent epochs")
        print(f"  🗑️  Deleting {len(dirs_to_delete)} old epochs")
        
        if dry_run:
            for d in dirs_to_delete:
                print(f"    🔍 Would delete: {os.path.basename(d)}")
            continue
        
        # Delete old epoch directories
        for d in dirs_to_delete:
            try:
                import shutil
                shutil.rmtree(d)
                print(f"    🗑️  Deleted: {os.path.basename(d)}")
            except Exception as e:
                print(f"    ❌ Failed to delete {os.path.basename(d)}: {e}")

def main():
    parser = argparse.ArgumentParser(description='Cleanup old checkpoints and inference samples')
    parser.add_argument('--checkpoint_dir', default='./checkpoints', 
                       help='Directory containing checkpoints')
    parser.add_argument('--keep_checkpoints', default=5, type=int,
                       help='Number of recent checkpoints to keep')
    parser.add_argument('--keep_inference_epochs', default=3, type=int,
                       help='Number of recent inference epochs to keep')
    parser.add_argument('--dry_run', action='store_true',
                       help='Show what would be deleted without actually deleting')
    
    args = parser.parse_args()
    
    print("="*60)
    print("Checkpoint Cleanup Tool")
    print("="*60)
    print(f"Directory: {args.checkpoint_dir}")
    print(f"Keep checkpoints: {args.keep_checkpoints}")
    print(f"Keep inference epochs: {args.keep_inference_epochs}")
    print(f"Dry run: {args.dry_run}")
    print("="*60)
    
    # Cleanup checkpoints
    cleanup_checkpoints(args.checkpoint_dir, args.keep_checkpoints, args.dry_run)
    
    # Cleanup inference samples
    cleanup_inference_samples(args.checkpoint_dir, args.keep_inference_epochs, args.dry_run)
    
    print("\n✅ Cleanup completed!")

if __name__ == "__main__":
    main()
