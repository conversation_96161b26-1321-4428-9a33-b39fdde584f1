# Checkpoint Management Guide

## 🎯 Problema Risolto

Ogni checkpoint SDCNet è ~1GB, quindi si accumula velocemente spazio su disco. Ho implementato un sistema di **cleanup automatico** che mantiene solo i modelli più importanti.

## 📊 Nuova Strategia di Salvataggio

### **Best Models (Più Importanti)**
- ✅ **Salvati**: Solo quando la validation loss migliora
- ✅ **Mantenuti**: Ultimi 3 per default (configurabile)
- ✅ **Naming**: `model_best_epoch_039_20250528_163004.pth`
- ✅ **Cleanup**: Automatico quando superano il limite

### **Regular Checkpoints (Meno Importanti)**
- ✅ **Salvati**: Ogni N epoch (default: 5)
- ✅ **Mantenuti**: Ultimi 2 per default (configurabile)
- ✅ **Naming**: `model_epoch_040.pth`
- ✅ **Cleanup**: Automatico e più aggressivo

## 🚀 Utilizzo

### **Training con Cleanup Automatico**
```bash
# Default: mantiene 3 best models + 2 checkpoint regolari
python train_sdcnet_with_masks.py \
  --train_file /path/to/dataset/train \
  --val_file /path/to/dataset/val \
  --resume "./checkpoints/masks_1_best_epoch_039_20250528_163004.pth" \
  --gpu 0

# Configurazione personalizzata
python train_sdcnet_with_masks.py \
  --train_file /path/to/dataset/train \
  --val_file /path/to/dataset/val \
  --keep_best_models 5 \        # Mantieni 5 best models
  --keep_regular_checkpoints 1 \ # Mantieni solo 1 checkpoint regolare
  --save_freq 10 \              # Salva ogni 10 epoch
  --gpu 0
```

### **Quick Test con Cleanup Ridotto**
```bash
# Per test: mantiene solo 2 best + 1 regular
python train_sdcnet_with_masks_quick_test.py \
  --train_file /path/to/dataset/train \
  --val_file /path/to/dataset/val \
  --gpu 0
```

## 📁 Struttura File Risultante

### **Prima (Problematico)**
```
checkpoints/
├── masks_1_epoch_035.pth    # 1.2 GB
├── masks_1_epoch_036.pth    # 1.2 GB
├── masks_1_epoch_037.pth    # 1.2 GB
├── masks_1_epoch_038.pth    # 1.2 GB
├── masks_1_epoch_039.pth    # 1.2 GB
├── masks_1_best.pth         # 1.2 GB
└── ...                      # 20+ GB totali!
```

### **Dopo (Ottimizzato)**
```
checkpoints/
├── masks_1_best_epoch_037_20250528_150000.pth  # 1.2 GB (3° migliore)
├── masks_1_best_epoch_038_20250528_160000.pth  # 1.2 GB (2° migliore)
├── masks_1_best_epoch_039_20250528_163004.pth  # 1.2 GB (migliore)
├── masks_1_epoch_038.pth                       # 1.2 GB (penultimo)
├── masks_1_epoch_039.pth                       # 1.2 GB (ultimo)
└── training_inference_*/                       # Campioni inferenza
Total: ~6 GB invece di 20+ GB
```

## 🔧 Parametri di Configurazione

| Parametro | Default | Descrizione | Raccomandato |
|-----------|---------|-------------|--------------|
| `--keep_best_models` | 3 | Best models da mantenere | 3-5 |
| `--keep_regular_checkpoints` | 2 | Checkpoint regolari da mantenere | 1-2 |
| `--save_freq` | 5 | Frequenza salvataggio regolare | 5-10 |

### **Configurazioni Raccomandate**

#### **Training Lungo (>100 epoch)**
```bash
--keep_best_models 5 \
--keep_regular_checkpoints 1 \
--save_freq 10
```
**Spazio**: ~7 GB massimo

#### **Training Medio (50-100 epoch)**
```bash
--keep_best_models 3 \
--keep_regular_checkpoints 2 \
--save_freq 5
```
**Spazio**: ~6 GB massimo

#### **Training Breve (<50 epoch)**
```bash
--keep_best_models 2 \
--keep_regular_checkpoints 1 \
--save_freq 3
```
**Spazio**: ~4 GB massimo

## 📊 Output del Cleanup

Durante il training vedrai messaggi come:
```
New best model saved: masks_1_best_epoch_042_20250528_170000.pth
🗑️  Removed old best model: masks_1_best_epoch_039_20250528_163004.pth (1.2 GB)
💾 Freed 1.2 GB by removing 1 old best models

Regular checkpoint saved: masks_1_epoch_045.pth
🗑️  Removed old checkpoint: masks_1_epoch_040.pth (1.1 GB)
💾 Freed 1.1 GB by removing 1 old checkpoints
```

## 🎯 Vantaggi

### **Spazio su Disco**
- ✅ **Riduzione**: Da 20+ GB a ~6 GB
- ✅ **Controllo**: Spazio massimo prevedibile
- ✅ **Automatico**: Nessun intervento manuale

### **Organizzazione**
- ✅ **Best Models**: Con timestamp per identificazione
- ✅ **Cronologia**: Mantiene progressi importanti
- ✅ **Flessibilità**: Configurabile per ogni scenario

### **Sicurezza**
- ✅ **Backup**: Sempre almeno 3 best models
- ✅ **Recovery**: Checkpoint recenti per ripresa
- ✅ **Graduale**: Rimozione progressiva, non drastica

## 🚨 Ripresa Training

### **Da Best Model (Raccomandato)**
```bash
# Trova l'ultimo best model
ls -la checkpoints/*_best_epoch_*.pth

# Riprendi dal migliore
python train_sdcnet_with_masks.py \
  --resume "./checkpoints/masks_1_best_epoch_042_20250528_170000.pth" \
  [altri parametri...]
```

### **Da Checkpoint Regolare**
```bash
# Trova l'ultimo checkpoint
ls -la checkpoints/*_epoch_*.pth

# Riprendi dall'ultimo
python train_sdcnet_with_masks.py \
  --resume "./checkpoints/masks_1_epoch_045.pth" \
  [altri parametri...]
```

## 🔧 Cleanup Manuale (Se Necessario)

Se hai già molti checkpoint vecchi:

```bash
# Vedi cosa può essere pulito
python cleanup_checkpoints.py --dry_run

# Pulisci manualmente
python cleanup_checkpoints.py --keep_checkpoints 3
```

## 📈 Monitoraggio Spazio

### **Durante Training**
```bash
# Windows
dir C:\ | findstr "bytes free"

# Controlla dimensione cartella checkpoints
dir checkpoints /s
```

### **Script di Monitoraggio**
```bash
# Crea un semplice script per monitorare
echo "Checkpoint directory size:"
du -sh checkpoints/
echo "Available disk space:"
df -h .
```

## ✅ Risultato Finale

Con questa implementazione:
- 🎯 **Spazio controllato**: Massimo 6-8 GB invece di 20+ GB
- 🎯 **Training continuo**: Nessuna interruzione per spazio
- 🎯 **Best models preservati**: Sempre i migliori risultati disponibili
- 🎯 **Configurabile**: Adattabile a ogni scenario
- 🎯 **Automatico**: Zero manutenzione richiesta

Il tuo training può ora continuare indefinitamente senza problemi di spazio! 🚀
