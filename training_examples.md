# Training Scripts Usage Examples

## 🚀 Quick Test Training (Recommended First Step)

Before running the full training, test everything with the quick test script:

### Command for Quick Test
```bash
python train_sdcnet_with_masks_quick_test.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0 \
  --batch_size 2 \
  --epochs 2 \
  --max_train_samples 20 \
  --max_val_samples 10 \
  --save_dir ./quick_test_checkpoints
```

### What the Quick Test Does
- ✅ **Only 2 epochs** - Fast completion
- ✅ **Limited samples** - 20 training, 10 validation
- ✅ **Small batches** - Reduced memory usage
- ✅ **GPU verification** - Confirms CUDA setup
- ✅ **Model validation** - Tests forward/backward pass
- ✅ **Inference monitoring** - Saves sample predictions
- ✅ **Progress bars** - tqdm visualization
- ✅ **Statistics logging** - Loss tracking

### Expected Output
```
SDCNet2DWithMasks QUICK TEST Training
============================================================
⚠️  THIS IS A QUICK TEST VERSION
   - Only 2 epochs
   - Max 20 training samples
   - Max 10 validation samples
   - Max 10 batches per epoch
============================================================
Device: cuda:0
Using GPU 0: NVIDIA GeForce RTX 3080
Total parameters: 39,175,123
Trainable parameters: 1,234,567

Epoch 1/2 (Quick Test): 100%|██████████| 10/10 [00:45<00:00,  4.5s/it]
Validation 1 (Quick Test): 100%|██████████| 5/5 [00:15<00:00,  3.0s/it]

Epoch 1 Results:
Train - Total: 0.8234, Color: 0.6123, Gradient: 0.1456, Smoothness: 0.0655
Val   - Total: 0.7891, Color: 0.5834, Gradient: 0.1398, Smoothness: 0.0659

QUICK TEST COMPLETED SUCCESSFULLY! ✅
```

---

## 🏋️ Full Training

After the quick test passes, run the full training:

### Basic Training Command
```bash
python train_sdcnet_with_masks.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0 \
  --epochs 100 \
  --batch_size 4 \
  --lr 0.0001 \
  --patience 10 \
  --save_dir ./checkpoints \
  --name sdcnet_with_masks_experiment_1
```

### Advanced Training Command
```bash
python train_sdcnet_with_masks.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --gpu 0 \
  --epochs 200 \
  --batch_size 8 \
  --val_batch_size 4 \
  --lr 0.0001 \
  --weight_decay 1e-4 \
  --patience 15 \
  --min_delta 0.001 \
  --workers 6 \
  --save_dir ./checkpoints \
  --name sdcnet_with_masks_final \
  --save_freq 5 \
  --inference_batches 1 500 1000 2000 \
  --inference_samples 3
```

### Resume Training
```bash
python train_sdcnet_with_masks.py \
  --train_file /path/to/your/dataset/train \
  --val_file /path/to/your/dataset/val \
  --resume ./checkpoints/sdcnet_with_masks_final_epoch_025.pth \
  --gpu 0
```

---

## 📊 Training Features

### 1. **GPU Support**
- Automatic GPU detection and setup
- CUDA memory optimization
- Mixed precision support (with `--fp16`)

### 2. **Progress Monitoring**
- **tqdm progress bars** for training and validation
- **Real-time loss display** in progress bars
- **Epoch statistics** printed after each epoch

### 3. **Early Stopping**
- **Patience-based stopping** (default: 10 epochs)
- **Minimum delta threshold** for improvement detection
- **Best model restoration** when stopping

### 4. **Visual Monitoring**
- **Inference samples** saved at specified batches (1, 1000, 2000)
- **Prediction vs target** comparisons
- **Input frames and masks** for reference
- **Loss information** saved as JSON

### 5. **Checkpointing**
- **Regular checkpoints** every N epochs
- **Best model saving** based on validation loss
- **Resume capability** from any checkpoint
- **Training configuration** saved as JSON

---

## 📁 Output Structure

### Training Outputs
```
checkpoints/
├── training_config.json                    # Training parameters
├── sdcnet_with_masks_epoch_005.pth        # Regular checkpoints
├── sdcnet_with_masks_epoch_010.pth
├── sdcnet_with_masks_best.pth             # Best model
└── training_inference_2024_01_15_14_30_25/
    ├── epoch_000/
    │   ├── batch_0001/
    │   │   ├── sample_0000/
    │   │   │   ├── prediction.png
    │   │   │   ├── target.png
    │   │   │   ├── input_frame_0.png
    │   │   │   ├── mask_0.png
    │   │   │   └── losses.json
    │   │   └── sample_0001/
    │   └── batch_1000/
    └── epoch_001/
```

### Loss Information (losses.json)
```json
{
  "total_loss": 0.8234,
  "color_loss": 0.6123,
  "gradient_loss": 0.1456,
  "smoothness_loss": 0.0655
}
```

---

## 🔧 Parameter Guidelines

### Memory Optimization
| GPU Memory | Batch Size | Val Batch Size | Workers |
|------------|------------|----------------|---------|
| 8GB        | 2-4        | 1-2           | 2-4     |
| 12GB       | 4-6        | 2-3           | 4-6     |
| 16GB+      | 6-8        | 3-4           | 6-8     |

### Learning Rate Schedule
- **Start**: 0.0001 (default)
- **High-res data**: 0.00005
- **Fine-tuning**: 0.00001

### Early Stopping
- **Patience**: 10-15 epochs
- **Min Delta**: 0.001-0.01
- **For quick experiments**: 5-7 epochs

---

## 🚨 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   ```bash
   # Reduce batch size
   --batch_size 2 --val_batch_size 1
   ```

2. **Dataset Loading Errors**
   ```bash
   # Check dataset structure
   python test_sdcnet_with_masks.py
   ```

3. **FlowNet2 Checkpoint Missing**
   ```
   Error: flownet2 checkpoint must be provided
   ```
   - Ensure checkpoint is at: `./flownet2_pytorch/FlowNet2_checkpoint.pth.tar`

4. **Slow Training**
   ```bash
   # Reduce workers if I/O bound
   --workers 2
   
   # Use smaller crop size
   --crop_size 224 288
   ```

### Performance Tips

1. **Use SSD storage** for dataset
2. **Pin memory** for faster GPU transfer
3. **Monitor GPU utilization** with `nvidia-smi`
4. **Use mixed precision** with `--fp16` if supported

---

## 📈 Expected Training Progress

### Typical Loss Curves
- **Initial loss**: ~1.0-2.0
- **After 10 epochs**: ~0.5-0.8
- **Converged**: ~0.2-0.4
- **Overfitting**: Validation loss increases

### Training Time Estimates
| Dataset Size | Batch Size | GPU | Time per Epoch |
|--------------|------------|-----|----------------|
| 1K samples   | 4          | RTX 3080 | ~2-3 min |
| 10K samples  | 4          | RTX 3080 | ~15-20 min |
| 100K samples | 4          | RTX 3080 | ~2-3 hours |

---

## ✅ Validation Checklist

Before full training:
- [ ] Quick test passes successfully
- [ ] GPU memory usage is acceptable
- [ ] Dataset loads without errors
- [ ] FlowNet2 checkpoint is available
- [ ] Inference samples look reasonable
- [ ] Loss values are decreasing

Ready for production training! 🚀
