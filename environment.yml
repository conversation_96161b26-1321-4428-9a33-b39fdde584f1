name: sdcnet
channels:
  - anaconda
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=1_gnu
  - blas=1.0=mkl
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2020.10.14=0
  - cairo=1.14.12=h8948797_3
  - certifi=2020.6.20=py36_0
  - cloudpickle=1.6.0=py_0
  - cycler=0.10.0=py_2
  - cytoolz=0.11.0=py36h8f6f2f9_3
  - dask-core=2021.3.0=pyhd8ed1ab_0
  - decorator=5.0.9=pyhd8ed1ab_0
  - ffmpeg=4.0=hcdf2ecd_0
  - fontconfig=2.13.0=h9420a91_0
  - freeglut=3.0.0=hf484d3e_5
  - freetype=2.10.4=h5ab3b9f_0
  - glib=2.56.2=hd408876_0
  - graphite2=1.3.14=h23475e2_0
  - harfbuzz=1.8.8=hffaf4a1_0
  - hdf5=1.10.2=hba1933b_1
  - icu=58.2=he6710b0_3
  - imagecodecs-lite=2019.12.3=py36h92226af_3
  - imageio=2.9.0=py_0
  - intel-openmp=2020.2=254
  - jasper=2.0.14=h07fcdf6_1
  - jpeg=9b=habf39ab_1
  - kiwisolver=1.3.1=py36h605e78d_1
  - lcms2=2.11=h396b838_0
  - ld_impl_linux-64=2.35.1=hea4e1c9_2
  - libffi=3.3=h58526e2_2
  - libgcc-ng=9.3.0=h2828fa1_19
  - libgfortran-ng=7.3.0=hdf63c60_0
  - libglu=9.0.0=hf484d3e_1
  - libgomp=9.3.0=h2828fa1_19
  - libopencv=3.4.2=hb342d67_1
  - libopus=1.3.1=h7b6447c_0
  - libpng=1.6.37=hbc83047_0
  - libprotobuf=3.17.2=h780b84a_1
  - libstdcxx-ng=9.3.0=h6de172a_19
  - libtiff=4.1.0=h2733197_1
  - libuuid=1.0.3=h1bed415_2
  - libvpx=1.7.0=h439df22_0
  - libxcb=1.14=h7b6447c_0
  - libxml2=2.9.10=hb55368b_3
  - lz4-c=1.9.2=heb0550a_3
  - matplotlib-base=3.3.4=py36hd391965_0
  - mkl=2019.4=243
  - mkl-service=2.3.0=py36he904b0f_0
  - mkl_fft=1.2.0=py36h23d657b_0
  - mkl_random=1.1.0=py36hd6b4f25_0
  - natsort=7.0.1=py_0
  - ncurses=6.2=h58526e2_4
  - networkx=2.5=py_0
  - olefile=0.46=py36_0
  - opencv=3.4.2=py36h6fd60c2_1
  - openssl=1.1.1k=h7f98852_0
  - pcre=8.44=he6710b0_0
  - pillow=8.0.0=py36h9a89aac_0
  - pip=21.1.2=pyhd8ed1ab_0
  - pixman=0.40.0=h7b6447c_0
  - protobuf=3.17.2=py36hc4f0c31_0
  - py-opencv=3.4.2=py36hb342d67_1
  - pyparsing=2.4.7=pyh9f0ad1d_0
  - python=3.6.13=hffdb5ce_0_cpython
  - python-dateutil=2.8.1=py_0
  - python_abi=3.6=1_cp36m
  - pywavelets=1.1.1=py36h92226af_3
  - pyyaml=5.4.1=py36h8f6f2f9_0
  - readline=8.1=h46c0cb4_0
  - scikit-image=0.17.2=py36h284efc9_4
  - scipy=1.5.2=py36h0b6359f_0
  - setuptools=49.6.0=py36h5fab9bb_3
  - six=1.15.0=py_0
  - sqlite=3.35.5=h74cdb3f_0
  - tensorboardx=2.4=pyhd8ed1ab_0
  - tifffile=2020.6.3=py_0
  - tk=8.6.10=h21135ba_1
  - toolz=0.11.1=py_0
  - tornado=6.1=py36h8f6f2f9_1
  - wheel=0.36.2=pyhd3deb0d_0
  - xz=5.2.5=h516909a_1
  - yaml=0.2.5=h516909a_0
  - zlib=1.2.11=h516909a_1010
  - zstd=1.4.4=h0b5b093_3
  - pip:
    - future==0.18.2
    - numpy==1.19.5
    - torch==1.6.0
    - tqdm==4.61.0
prefix: /home/<USER>/anaconda3/envs/sdcnet
