ninja_required_version = 1.3
cxx = cl
nvcc = C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin\nvcc

cflags = /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include\torch\csrc\api\include -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include\TH -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include\THC "-IC:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\include" -IC:\ProgramData\anaconda3\include -IC:\ProgramData\anaconda3\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include" "-IC:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt"
post_cflags = -std=c++14 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=channelnorm_cuda -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
cuda_cflags = -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include\torch\csrc\api\include -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include\TH -IC:\ProgramData\anaconda3\Lib\site-packages\torch\include\THC "-IC:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\include" -IC:\ProgramData\anaconda3\include -IC:\ProgramData\anaconda3\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include" "-IC:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt"
cuda_post_cflags = -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -gencode arch=compute_52,code=sm_52 -gencode arch=compute_60,code=sm_60 -gencode arch=compute_61,code=sm_61 -gencode arch=compute_70,code=sm_70 -gencode arch=compute_70,code=compute_70 -gencode arch=compute_75,code=sm_75 -gencode arch=compute_75,code=compute_75 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=channelnorm_cuda -D_GLIBCXX_USE_CXX11_ABI=0
cuda_dlink_post_cflags = 
ldflags = 

rule compile
  command = cl /showIncludes $cflags -c $in /Fo$out $post_cflags
  deps = msvc

rule cuda_compile
  depfile = $out.d
  deps = gcc
  command = $nvcc --generate-dependencies-with-compile --dependency-output $out.d $cuda_cflags -c $in -o $out $cuda_post_cflags





build C$:\Users\nessu\OneDrive\Desktop\tesi_modificata\zl-lvc-master\WP1\extrapolation\sdcnet\flownet2_pytorch\networks\channelnorm_package\build\temp.win-amd64-cpython-311\Release\channelnorm_cuda.obj: compile C$:\Users\nessu\OneDrive\Desktop\tesi_modificata\zl-lvc-master\WP1\extrapolation\sdcnet\flownet2_pytorch\networks\channelnorm_package\channelnorm_cuda.cc
build C$:\Users\nessu\OneDrive\Desktop\tesi_modificata\zl-lvc-master\WP1\extrapolation\sdcnet\flownet2_pytorch\networks\channelnorm_package\build\temp.win-amd64-cpython-311\Release\channelnorm_kernel.obj: cuda_compile C$:\Users\nessu\OneDrive\Desktop\tesi_modificata\zl-lvc-master\WP1\extrapolation\sdcnet\flownet2_pytorch\networks\channelnorm_package\channelnorm_kernel.cu






