#!/usr/bin/env python3
"""
Test script to verify the flow bias fix
This script compares the original model with the fixed version to show the difference.
"""

import torch
import numpy as np
import cv2
import os
import sys
from datetime import datetime

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d_with_masks import SDCNet2DWithMasks
from models.sdc_net2d_with_masks_configurable import SDCNet2DWithMasksConfigurable
import argparse

def create_test_args():
    """Create test arguments"""
    args = argparse.Namespace()
    
    # Model parameters
    args.sequence_length = 2
    args.rgb_max = 255.0
    args.flownet2_checkpoint = './flownet2_pytorch/FlowNet2_checkpoint.pth.tar'
    
    # Dataset parameters
    args.sample_rate = 1
    args.crop_size = [256, 320]
    args.start_index = 0
    args.stride = 64
    
    # Flow statistics (for configurable version)
    args.flow_mean_x = 0.0  # Fixed: no bias
    args.flow_mean_y = 0.0  # Fixed: no bias
    args.flow_std_x = 13.77204132
    args.flow_std_y = 7.47463894
    
    return args

def create_test_input():
    """Create a simple test input with static images"""
    batch_size = 1
    height, width = 256, 320
    
    # Create a simple test pattern - a white square on black background
    base_image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add a white square in the center
    center_y, center_x = height // 2, width // 2
    square_size = 50
    base_image[center_y-square_size:center_y+square_size, 
               center_x-square_size:center_x+square_size] = 255
    
    # Create three identical frames (no motion)
    frames = [base_image.copy() for _ in range(3)]
    
    # Create binary masks - foreground where the square is
    mask = np.zeros((height, width), dtype=np.uint8)
    mask[center_y-square_size:center_y+square_size, 
         center_x-square_size:center_x+square_size] = 255
    masks = [mask.copy() for _ in range(3)]
    
    # Convert to tensors
    input_dict = {
        'image': [torch.from_numpy(frame.transpose(2, 0, 1)).float().unsqueeze(0) 
                 for frame in frames],
        'mask': [torch.from_numpy(mask_img).float().unsqueeze(0).unsqueeze(0) 
                for mask_img in masks]
    }
    
    return input_dict, base_image

def test_model_bias(model, input_dict, model_name):
    """Test a model and return the prediction"""
    model.eval()
    
    with torch.no_grad():
        # Move to GPU if available
        if torch.cuda.is_available():
            model = model.cuda()
            for key in input_dict:
                input_dict[key] = [tensor.cuda() for tensor in input_dict[key]]
        
        # Forward pass
        losses, prediction, target = model(input_dict)
        
        # Move back to CPU for analysis
        prediction = prediction.cpu()
        target = target.cpu()
        
        # Convert to numpy
        pred_np = prediction[0].numpy().transpose(1, 2, 0)  # CHW -> HWC
        target_np = target[0].numpy().transpose(1, 2, 0)
        
        # Clip to valid range
        pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)
        target_np = np.clip(target_np, 0, 255).astype(np.uint8)
        
        print(f"\n{model_name} Results:")
        print(f"  Total Loss: {losses['tot'].item():.6f}")
        print(f"  Color Loss: {losses['color'].item():.6f}")
        print(f"  Gradient Loss: {losses['color_gradient'].item():.6f}")
        print(f"  Smoothness Loss: {losses['flow_smoothness'].item():.6f}")
        
        return pred_np, target_np, losses

def analyze_shift(prediction, target, model_name):
    """Analyze if there's a systematic shift in the prediction"""
    # Convert to grayscale for easier analysis
    pred_gray = cv2.cvtColor(prediction, cv2.COLOR_BGR2GRAY)
    target_gray = cv2.cvtColor(target, cv2.COLOR_BGR2GRAY)
    
    # Find the center of mass of the white square
    def find_center_of_mass(img):
        # Threshold to find the white regions
        _, thresh = cv2.threshold(img, 127, 255, cv2.THRESH_BINARY)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # Get the largest contour (should be our square)
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Calculate center of mass
            M = cv2.moments(largest_contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                return cx, cy
        
        return None, None
    
    target_cx, target_cy = find_center_of_mass(target_gray)
    pred_cx, pred_cy = find_center_of_mass(pred_gray)
    
    if target_cx is not None and pred_cx is not None:
        shift_x = pred_cx - target_cx
        shift_y = pred_cy - target_cy
        
        print(f"\n{model_name} Shift Analysis:")
        print(f"  Target center: ({target_cx}, {target_cy})")
        print(f"  Prediction center: ({pred_cx}, {pred_cy})")
        print(f"  Shift: ({shift_x:+d}, {shift_y:+d}) pixels")
        
        if abs(shift_x) > 2 or abs(shift_y) > 2:
            print(f"  ⚠️  Significant shift detected!")
        else:
            print(f"  ✅ No significant shift")
            
        return shift_x, shift_y
    else:
        print(f"\n{model_name} Shift Analysis:")
        print(f"  ❌ Could not detect object in prediction or target")
        return None, None

def save_comparison_images(pred_original, pred_fixed, target, save_dir):
    """Save comparison images"""
    os.makedirs(save_dir, exist_ok=True)
    
    # Save individual images
    cv2.imwrite(os.path.join(save_dir, "target.png"), target)
    cv2.imwrite(os.path.join(save_dir, "prediction_original.png"), pred_original)
    cv2.imwrite(os.path.join(save_dir, "prediction_fixed.png"), pred_fixed)
    
    # Create a comparison image
    comparison = np.hstack([target, pred_original, pred_fixed])
    
    # Add labels
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(comparison, "Target", (10, 30), font, 1, (255, 255, 255), 2)
    cv2.putText(comparison, "Original (Biased)", (target.shape[1] + 10, 30), font, 1, (255, 255, 255), 2)
    cv2.putText(comparison, "Fixed (No Bias)", (target.shape[1] * 2 + 10, 30), font, 1, (255, 255, 255), 2)
    
    cv2.imwrite(os.path.join(save_dir, "comparison.png"), comparison)
    print(f"\nComparison images saved to: {save_dir}")

def main():
    print("="*60)
    print("Flow Bias Fix Test")
    print("="*60)
    
    # Check if FlowNet2 checkpoint exists
    args = create_test_args()
    if not os.path.exists(args.flownet2_checkpoint):
        print(f"❌ FlowNet2 checkpoint not found at: {args.flownet2_checkpoint}")
        print("Please ensure the checkpoint is available for this test.")
        return
    
    print("✅ FlowNet2 checkpoint found")
    
    # Create test input
    print("\n📝 Creating test input (static white square)...")
    input_dict, base_image = create_test_input()
    print("✅ Test input created")
    
    # Test original model (with bias)
    print("\n🔍 Testing original model (with potential bias)...")
    try:
        # Temporarily modify the original model to use the old biased values
        original_args = create_test_args()
        model_original = SDCNet2DWithMasks(original_args)
        
        # Manually set the biased flow_mean for comparison
        biased_flow_mean = torch.FloatTensor([-0.94427323, -1.23077035]).view(1, 2, 1, 1)
        model_original.flow_mean.data.copy_(biased_flow_mean)
        
        pred_original, target, losses_original = test_model_bias(model_original, input_dict, "Original (Biased)")
        
    except Exception as e:
        print(f"❌ Error testing original model: {e}")
        return
    
    # Test fixed model (no bias)
    print("\n🔧 Testing fixed model (no bias)...")
    try:
        fixed_args = create_test_args()
        model_fixed = SDCNet2DWithMasksConfigurable(fixed_args)
        
        pred_fixed, _, losses_fixed = test_model_bias(model_fixed, input_dict, "Fixed (No Bias)")
        
    except Exception as e:
        print(f"❌ Error testing fixed model: {e}")
        return
    
    # Analyze shifts
    print("\n📊 Analyzing systematic shifts...")
    shift_original = analyze_shift(pred_original, target, "Original Model")
    shift_fixed = analyze_shift(pred_fixed, target, "Fixed Model")
    
    # Save comparison images
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f"flow_bias_test_{timestamp}"
    save_comparison_images(pred_original, pred_fixed, target, save_dir)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if shift_original and shift_fixed:
        print(f"Original model shift: ({shift_original[0]:+d}, {shift_original[1]:+d}) pixels")
        print(f"Fixed model shift:    ({shift_fixed[0]:+d}, {shift_fixed[1]:+d}) pixels")
        
        improvement_x = abs(shift_original[0]) - abs(shift_fixed[0])
        improvement_y = abs(shift_original[1]) - abs(shift_fixed[1])
        
        if improvement_x > 0 or improvement_y > 0:
            print(f"✅ Improvement detected! Bias reduced by ({improvement_x:+d}, {improvement_y:+d}) pixels")
        else:
            print(f"⚠️  No significant improvement detected")
    
    print(f"\nLoss comparison:")
    print(f"  Original total loss: {losses_original['tot'].item():.6f}")
    print(f"  Fixed total loss:    {losses_fixed['tot'].item():.6f}")
    
    print(f"\n📁 Results saved to: {save_dir}")
    print("\nTo use the fixed model in training, use:")
    print("  --model SDCNet2DWithMasks  (flow_mean set to [0.0, 0.0])")
    print("or")
    print("  --model SDCNet2DWithMasksConfigurable --flow_mean_x 0.0 --flow_mean_y 0.0")

if __name__ == "__main__":
    main()
