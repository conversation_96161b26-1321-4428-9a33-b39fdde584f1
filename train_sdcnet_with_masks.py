#!/usr/bin/env python3
"""
Training script for SDCNet2DWithMasks
Enhanced with GPU support, tqdm progress bars, early stopping, and visual inference monitoring.
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import cv2
from datetime import datetime
import json
import shutil

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d_with_masks import SDCNet2DWithMasks
from datasets.frame_loader_with_masks import FrameLoaderWithMasks
import tools

class EarlyStopping:
    def __init__(self, patience=10, min_delta=0.001, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None

    def __call__(self, val_loss, model):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1

        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights is not None:
                model.load_state_dict(self.best_weights)
            return True
        return False

class TrainingStats:
    def __init__(self):
        self.reset()

    def reset(self):
        self.losses = {'total': [], 'color': [], 'color_gradient': [], 'flow_smoothness': []}

    def update(self, losses):
        for key in self.losses:
            if key in losses:
                self.losses[key].append(losses[key])

    def get_averages(self):
        return {key: np.mean(values) if values else 0.0 for key, values in self.losses.items()}

def create_args():
    parser = argparse.ArgumentParser(description='SDCNet2DWithMasks Training')

    # Model parameters
    parser.add_argument('--model', default='SDCNet2DWithMasks', type=str)
    parser.add_argument('--dataset', default='FrameLoaderWithMasks', type=str)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint',
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)

    # Training parameters
    parser.add_argument('--epochs', default=100, type=int)
    parser.add_argument('--batch_size', default=4, type=int)
    parser.add_argument('--val_batch_size', default=2, type=int)
    parser.add_argument('--lr', default=0.0001, type=float)
    parser.add_argument('--weight_decay', default=1e-4, type=float)
    parser.add_argument('--workers', default=4, type=int)

    # Dataset parameters
    parser.add_argument('--train_file', required=True, type=str)
    parser.add_argument('--val_file', required=True, type=str)
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)

    # Output parameters
    parser.add_argument('--save_dir', default='./checkpoints', type=str)
    parser.add_argument('--name', default='sdcnet_with_masks', type=str)
    parser.add_argument('--resume', default='', type=str)

    # Early stopping
    parser.add_argument('--patience', default=10, type=int)
    parser.add_argument('--min_delta', default=0.001, type=float)

    # GPU settings
    parser.add_argument('--gpu', default=0, type=int)
    parser.add_argument('--fp16', action='store_true')

    # Monitoring
    parser.add_argument('--save_freq', default=5, type=int)
    parser.add_argument('--inference_batches', default=[1, 1000, 2000], nargs='+', type=int)
    parser.add_argument('--inference_samples', default=3, type=int)

    # Checkpoint management
    parser.add_argument('--keep_best_models', default=3, type=int,
                       help='Number of best models to keep (default: 3)')
    parser.add_argument('--keep_regular_checkpoints', default=2, type=int,
                       help='Number of regular checkpoints to keep (default: 2)')

    return parser.parse_args()

def setup_gpu(gpu_id):
    """Setup GPU device"""
    if torch.cuda.is_available():
        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
        torch.cuda.set_device(0)
        print(f"Using GPU {gpu_id}: {torch.cuda.get_device_name(0)}")
        return True
    else:
        print("CUDA not available, using CPU")
        return False

def save_inference_samples(model, dataloader, epoch, batch_idx, save_dir, num_samples=3):
    """Save inference samples for visual monitoring"""
    model.eval()

    # Create save directory
    timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    if not hasattr(save_inference_samples, 'base_dir'):
        save_inference_samples.base_dir = os.path.join(save_dir, f"training_inference_{timestamp}")

    epoch_dir = os.path.join(save_inference_samples.base_dir, f"epoch_{epoch:03d}")
    batch_dir = os.path.join(epoch_dir, f"batch_{batch_idx:04d}")
    os.makedirs(batch_dir, exist_ok=True)

    with torch.no_grad():
        for i, batch in enumerate(dataloader):
            if i >= num_samples:
                break

            # Move to GPU
            inputs = {}
            for key in ['image', 'mask']:
                if key in batch:
                    inputs[key] = [tensor.cuda() for tensor in batch[key]]

            # Forward pass
            losses, prediction, target = model(inputs)

            # Convert to numpy and denormalize
            pred_np = prediction[0].cpu().numpy().transpose(1, 2, 0)  # CHW -> HWC
            target_np = target[0].cpu().numpy().transpose(1, 2, 0)

            # Clip values to valid range
            pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)
            target_np = np.clip(target_np, 0, 255).astype(np.uint8)

            # Save images
            sample_dir = os.path.join(batch_dir, f"sample_{i:04d}")
            os.makedirs(sample_dir, exist_ok=True)

            cv2.imwrite(os.path.join(sample_dir, "prediction.png"), pred_np)
            cv2.imwrite(os.path.join(sample_dir, "target.png"), target_np)

            # Save input frames and masks for reference
            for j, frame in enumerate(inputs['image'][:-1]):  # Exclude target frame
                frame_np = frame[0].cpu().numpy().transpose(1, 2, 0)
                frame_np = np.clip(frame_np, 0, 255).astype(np.uint8)
                cv2.imwrite(os.path.join(sample_dir, f"input_frame_{j}.png"), frame_np)

            for j, mask in enumerate(inputs['mask']):
                mask_np = mask[0, 0].cpu().numpy()  # Remove batch and channel dims
                mask_np = np.clip(mask_np, 0, 255).astype(np.uint8)
                cv2.imwrite(os.path.join(sample_dir, f"mask_{j}.png"), mask_np)

            # Save loss info
            loss_info = {
                'total_loss': losses['tot'].item(),
                'color_loss': losses['color'].item(),
                'gradient_loss': losses['color_gradient'].item(),
                'smoothness_loss': losses['flow_smoothness'].item()
            }

            with open(os.path.join(sample_dir, "losses.json"), 'w') as f:
                json.dump(loss_info, f, indent=2)

    model.train()
    print(f"Saved inference samples to {batch_dir}")

def train_epoch(model, train_loader, optimizer, epoch, args, device):
    """Train for one epoch"""
    model.train()
    stats = TrainingStats()

    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{args.epochs}')

    for batch_idx, batch in enumerate(pbar):
        # Move to GPU
        inputs = {}
        for key in ['image', 'mask']:
            if key in batch:
                inputs[key] = [tensor.to(device) for tensor in batch[key]]

        # Forward pass
        optimizer.zero_grad()
        losses, prediction, target = model(inputs)

        # Backward pass
        total_loss = losses['tot']
        total_loss.backward()
        optimizer.step()

        # Update statistics
        loss_dict = {
            'total': total_loss.item(),
            'color': losses['color'].item(),
            'color_gradient': losses['color_gradient'].item(),
            'flow_smoothness': losses['flow_smoothness'].item()
        }
        stats.update(loss_dict)

        # Update progress bar
        pbar.set_postfix({
            'Loss': f"{total_loss.item():.4f}",
            'Color': f"{losses['color'].item():.4f}",
            'Grad': f"{losses['color_gradient'].item():.4f}",
            'Smooth': f"{losses['flow_smoothness'].item():.4f}"
        })

        # Save inference samples at specified batches
        if batch_idx + 1 in args.inference_batches:
            save_inference_samples(model, train_loader, epoch, batch_idx + 1,
                                 args.save_dir, args.inference_samples)

    return stats.get_averages()

def validate_epoch(model, val_loader, epoch, args, device):
    """Validate for one epoch"""
    model.eval()
    stats = TrainingStats()

    with torch.no_grad():
        pbar = tqdm(val_loader, desc=f'Validation {epoch+1}')

        for batch in pbar:
            # Move to GPU
            inputs = {}
            for key in ['image', 'mask']:
                if key in batch:
                    inputs[key] = [tensor.to(device) for tensor in batch[key]]

            # Forward pass
            losses, prediction, target = model(inputs)

            # Update statistics
            loss_dict = {
                'total': losses['tot'].item(),
                'color': losses['color'].item(),
                'color_gradient': losses['color_gradient'].item(),
                'flow_smoothness': losses['flow_smoothness'].item()
            }
            stats.update(loss_dict)

            # Update progress bar
            pbar.set_postfix({
                'Val Loss': f"{losses['tot'].item():.4f}"
            })

    return stats.get_averages()

def save_checkpoint(model, optimizer, epoch, train_stats, val_stats, args, is_best=False):
    """Save model checkpoint with automatic cleanup"""
    os.makedirs(args.save_dir, exist_ok=True)

    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_stats': train_stats,
        'val_stats': val_stats,
        'args': vars(args),
        'val_loss': val_stats['total']  # Store validation loss for comparison
    }

    # Only save if it's a best model or regular interval
    if is_best:
        # Save new best model with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        best_path = os.path.join(args.save_dir, f'{args.name}_best_epoch_{epoch:03d}_{timestamp}.pth')
        torch.save(checkpoint, best_path)
        print(f"New best model saved: {best_path}")

        # Cleanup old best models (configurable)
        cleanup_old_best_models(args.save_dir, args.name, keep_count=args.keep_best_models)

    elif (epoch + 1) % args.save_freq == 0:
        # Save regular checkpoint (these will be cleaned up more aggressively)
        checkpoint_path = os.path.join(args.save_dir, f'{args.name}_epoch_{epoch:03d}.pth')
        torch.save(checkpoint, checkpoint_path)
        print(f"Regular checkpoint saved: {checkpoint_path}")

        # Cleanup old regular checkpoints (configurable)
        cleanup_old_regular_checkpoints(args.save_dir, args.name, keep_count=args.keep_regular_checkpoints)

def cleanup_old_best_models(save_dir, model_name, keep_count=3):
    """Keep only the most recent best models"""
    import glob

    # Find all best model files
    pattern = os.path.join(save_dir, f'{model_name}_best_epoch_*.pth')
    best_files = glob.glob(pattern)

    if len(best_files) <= keep_count:
        return

    # Sort by modification time (newest first)
    best_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

    # Delete old files
    files_to_delete = best_files[keep_count:]
    total_size_mb = 0

    for file_path in files_to_delete:
        try:
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            total_size_mb += size_mb
            os.remove(file_path)
            print(f"🗑️  Removed old best model: {os.path.basename(file_path)} ({size_mb:.1f} MB)")
        except Exception as e:
            print(f"❌ Failed to remove {os.path.basename(file_path)}: {e}")

    if total_size_mb > 0:
        print(f"💾 Freed {total_size_mb:.1f} MB by removing {len(files_to_delete)} old best models")

def cleanup_old_regular_checkpoints(save_dir, model_name, keep_count=2):
    """Keep only the most recent regular checkpoints"""
    import glob

    # Find all regular checkpoint files (exclude best models)
    pattern = os.path.join(save_dir, f'{model_name}_epoch_*.pth')
    checkpoint_files = glob.glob(pattern)

    # Filter out best model files
    checkpoint_files = [f for f in checkpoint_files if '_best_' not in f]

    if len(checkpoint_files) <= keep_count:
        return

    # Sort by modification time (newest first)
    checkpoint_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

    # Delete old files
    files_to_delete = checkpoint_files[keep_count:]
    total_size_mb = 0

    for file_path in files_to_delete:
        try:
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            total_size_mb += size_mb
            os.remove(file_path)
            print(f"🗑️  Removed old checkpoint: {os.path.basename(file_path)} ({size_mb:.1f} MB)")
        except Exception as e:
            print(f"❌ Failed to remove {os.path.basename(file_path)}: {e}")

    if total_size_mb > 0:
        print(f"💾 Freed {total_size_mb:.1f} MB by removing {len(files_to_delete)} old checkpoints")

def main():
    args = create_args()

    # Setup GPU
    use_cuda = setup_gpu(args.gpu)
    device = torch.device('cuda' if use_cuda else 'cpu')

    # Create save directory
    os.makedirs(args.save_dir, exist_ok=True)

    # Save training configuration
    with open(os.path.join(args.save_dir, 'training_config.json'), 'w') as f:
        json.dump(vars(args), f, indent=2)

    print("="*60)
    print("SDCNet2DWithMasks Training")
    print("="*60)
    print(f"Device: {device}")
    print(f"Model: {args.model}")
    print(f"Dataset: {args.dataset}")
    print(f"Epochs: {args.epochs}")
    print(f"Batch size: {args.batch_size}")
    print(f"Learning rate: {args.lr}")
    print(f"Early stopping patience: {args.patience}")
    print("="*60)

    # Create model
    model = SDCNet2DWithMasks(args)
    model = model.to(device)

    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # Create optimizer
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)

    # Create datasets
    train_dataset = FrameLoaderWithMasks(args, args.train_file, is_training=True)
    val_dataset = FrameLoaderWithMasks(args, args.val_file, is_training=False)

    train_loader = DataLoader(train_dataset, batch_size=args.batch_size,
                             shuffle=True, num_workers=args.workers, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=args.val_batch_size,
                           shuffle=False, num_workers=args.workers, pin_memory=True)

    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    print(f"Training batches: {len(train_loader)}")
    print(f"Validation batches: {len(val_loader)}")

    # Early stopping
    early_stopping = EarlyStopping(patience=args.patience, min_delta=args.min_delta)

    # Training loop
    best_val_loss = float('inf')
    start_epoch = 0

    # Resume from checkpoint if specified
    if args.resume and os.path.exists(args.resume):
        print(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        best_val_loss = checkpoint['val_stats']['total']
        print(f"Resumed from epoch {start_epoch}")

    print("\nStarting training...")

    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}")
        print("-" * 50)

        # Train
        train_stats = train_epoch(model, train_loader, optimizer, epoch, args, device)

        # Validate
        val_stats = validate_epoch(model, val_loader, epoch, args, device)

        # Print epoch statistics
        print(f"\nEpoch {epoch+1} Results:")
        print(f"Train - Total: {train_stats['total']:.4f}, Color: {train_stats['color']:.4f}, "
              f"Gradient: {train_stats['color_gradient']:.4f}, Smoothness: {train_stats['flow_smoothness']:.4f}")
        print(f"Val   - Total: {val_stats['total']:.4f}, Color: {val_stats['color']:.4f}, "
              f"Gradient: {val_stats['color_gradient']:.4f}, Smoothness: {val_stats['flow_smoothness']:.4f}")

        # Check for best model
        is_best = val_stats['total'] < best_val_loss
        if is_best:
            best_val_loss = val_stats['total']

        # Save checkpoint
        if (epoch + 1) % args.save_freq == 0 or is_best:
            save_checkpoint(model, optimizer, epoch, train_stats, val_stats, args, is_best)

        # Early stopping check
        if early_stopping(val_stats['total'], model):
            print(f"\nEarly stopping triggered after {epoch+1} epochs")
            print(f"Best validation loss: {early_stopping.best_loss:.4f}")
            break

    print("\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.4f}")

if __name__ == "__main__":
    main()
