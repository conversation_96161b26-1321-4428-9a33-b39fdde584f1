#!/usr/bin/env python3
"""
Quick test training script for SDCNet2DWithMasks
Reduced version for testing with 2 epochs and limited dataset.
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
import numpy as np
from tqdm import tqdm
import cv2
from datetime import datetime
import json
import shutil

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d_with_masks import SDCNet2DWithMasks
from models.sdc_net2d_with_masks_configurable import SDCNet2DWithMasksConfigurable
from datasets.frame_loader_with_masks import FrameLoaderWithMasks
import tools

class TrainingStats:
    def __init__(self):
        self.reset()

    def reset(self):
        self.losses = {'total': [], 'color': [], 'color_gradient': [], 'flow_smoothness': []}

    def update(self, losses):
        for key in self.losses:
            if key in losses:
                self.losses[key].append(losses[key])

    def get_averages(self):
        return {key: np.mean(values) if values else 0.0 for key, values in self.losses.items()}

def create_args():
    parser = argparse.ArgumentParser(description='SDCNet2DWithMasks Quick Test Training')

    # Model parameters
    parser.add_argument('--model', default='SDCNet2DWithMasks', type=str,
                       choices=['SDCNet2DWithMasks', 'SDCNet2DWithMasksConfigurable'],
                       help='Model to use. SDCNet2DWithMasks has flow_mean=[0,0], SDCNet2DWithMasksConfigurable allows custom flow statistics')
    parser.add_argument('--dataset', default='FrameLoaderWithMasks', type=str)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint',
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)

    # Training parameters (REDUCED FOR TESTING)
    parser.add_argument('--epochs', default=2, type=int, help='Reduced to 2 for quick test')
    parser.add_argument('--batch_size', default=2, type=int, help='Reduced batch size')
    parser.add_argument('--val_batch_size', default=1, type=int)
    parser.add_argument('--lr', default=0.0001, type=float)
    parser.add_argument('--weight_decay', default=1e-4, type=float)
    parser.add_argument('--workers', default=2, type=int, help='Reduced workers')

    # Dataset parameters
    parser.add_argument('--train_file', required=True, type=str)
    parser.add_argument('--val_file', required=True, type=str)
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)

    # TESTING PARAMETERS
    parser.add_argument('--max_train_samples', default=20, type=int,
                       help='Limit training samples for quick test')
    parser.add_argument('--max_val_samples', default=10, type=int,
                       help='Limit validation samples for quick test')
    parser.add_argument('--max_batches_per_epoch', default=10, type=int,
                       help='Limit batches per epoch for quick test')

    # Output parameters
    parser.add_argument('--save_dir', default='./quick_test_checkpoints', type=str)
    parser.add_argument('--name', default='sdcnet_with_masks_quick_test', type=str)
    parser.add_argument('--resume', default='', type=str)

    # GPU settings
    parser.add_argument('--gpu', default=0, type=int)
    parser.add_argument('--fp16', action='store_true')

    # Flow statistics (for SDCNet2DWithMasksConfigurable)
    parser.add_argument('--flow_mean_x', default=0.0, type=float,
                       help='Flow mean for X direction (default: 0.0 to avoid bias)')
    parser.add_argument('--flow_mean_y', default=0.0, type=float,
                       help='Flow mean for Y direction (default: 0.0 to avoid bias)')
    parser.add_argument('--flow_std_x', default=13.77204132, type=float,
                       help='Flow std for X direction')
    parser.add_argument('--flow_std_y', default=7.47463894, type=float,
                       help='Flow std for Y direction')

    # Monitoring (REDUCED)
    parser.add_argument('--save_freq', default=1, type=int, help='Save every epoch for testing')
    parser.add_argument('--inference_batches', default=[1, 5], nargs='+', type=int,
                       help='Reduced inference monitoring')
    parser.add_argument('--inference_samples', default=2, type=int, help='Reduced samples')

    return parser.parse_args()

def setup_gpu(gpu_id):
    """Setup GPU device"""
    if torch.cuda.is_available():
        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
        torch.cuda.set_device(0)
        print(f"Using GPU {gpu_id}: {torch.cuda.get_device_name(0)}")
        return True
    else:
        print("CUDA not available, using CPU")
        return False

def save_inference_samples(model, dataloader, epoch, batch_idx, save_dir, num_samples=2):
    """Save inference samples for visual monitoring (reduced version)"""
    model.eval()

    # Create save directory
    timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    if not hasattr(save_inference_samples, 'base_dir'):
        save_inference_samples.base_dir = os.path.join(save_dir, f"training_inference_{timestamp}")

    epoch_dir = os.path.join(save_inference_samples.base_dir, f"epoch_{epoch:03d}")
    batch_dir = os.path.join(epoch_dir, f"batch_{batch_idx:04d}")
    os.makedirs(batch_dir, exist_ok=True)

    with torch.no_grad():
        # Get a single batch for inference
        try:
            batch = next(iter(dataloader))
        except:
            print("Could not get batch for inference")
            model.train()
            return

        # Move to GPU
        inputs = {}
        for key in ['image', 'mask']:
            if key in batch:
                inputs[key] = [tensor.cuda() for tensor in batch[key]]

        # Forward pass on first few samples
        losses, prediction, target = model(inputs)

        # Save only first sample to save time
        for i in range(min(num_samples, prediction.shape[0])):
            # Convert to numpy and denormalize
            pred_np = prediction[i].cpu().numpy().transpose(1, 2, 0)  # CHW -> HWC
            target_np = target[i].cpu().numpy().transpose(1, 2, 0)

            # Clip values to valid range
            pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)
            target_np = np.clip(target_np, 0, 255).astype(np.uint8)

            # Save images
            sample_dir = os.path.join(batch_dir, f"sample_{i:04d}")
            os.makedirs(sample_dir, exist_ok=True)

            cv2.imwrite(os.path.join(sample_dir, "prediction.png"), pred_np)
            cv2.imwrite(os.path.join(sample_dir, "target.png"), target_np)

            # Save one input frame and mask for reference
            if len(inputs['image']) > 0:
                frame_np = inputs['image'][0][i].cpu().numpy().transpose(1, 2, 0)
                frame_np = np.clip(frame_np, 0, 255).astype(np.uint8)
                cv2.imwrite(os.path.join(sample_dir, "input_frame.png"), frame_np)

            if len(inputs['mask']) > 0:
                mask_np = inputs['mask'][0][i, 0].cpu().numpy()  # Remove channel dim
                mask_np = np.clip(mask_np, 0, 255).astype(np.uint8)
                cv2.imwrite(os.path.join(sample_dir, "mask.png"), mask_np)

            # Save loss info
            loss_info = {
                'total_loss': losses['tot'].item(),
                'color_loss': losses['color'].item(),
                'gradient_loss': losses['color_gradient'].item(),
                'smoothness_loss': losses['flow_smoothness'].item()
            }

            with open(os.path.join(sample_dir, "losses.json"), 'w') as f:
                json.dump(loss_info, f, indent=2)

    model.train()
    print(f"Saved inference samples to {batch_dir}")

def train_epoch(model, train_loader, optimizer, epoch, args, device):
    """Train for one epoch (limited for testing)"""
    model.train()
    stats = TrainingStats()

    # Limit batches for quick test
    max_batches = min(len(train_loader), args.max_batches_per_epoch)

    pbar = tqdm(enumerate(train_loader), total=max_batches,
                desc=f'Epoch {epoch+1}/{args.epochs} (Quick Test)')

    for batch_idx, batch in pbar:
        if batch_idx >= max_batches:
            break

        # Move to GPU
        inputs = {}
        for key in ['image', 'mask']:
            if key in batch:
                inputs[key] = [tensor.to(device) for tensor in batch[key]]

        # Forward pass
        optimizer.zero_grad()
        losses, prediction, target = model(inputs)

        # Backward pass
        total_loss = losses['tot']
        total_loss.backward()
        optimizer.step()

        # Update statistics
        loss_dict = {
            'total': total_loss.item(),
            'color': losses['color'].item(),
            'color_gradient': losses['color_gradient'].item(),
            'flow_smoothness': losses['flow_smoothness'].item()
        }
        stats.update(loss_dict)

        # Update progress bar
        pbar.set_postfix({
            'Loss': f"{total_loss.item():.4f}",
            'Color': f"{losses['color'].item():.4f}",
            'Grad': f"{losses['color_gradient'].item():.4f}",
            'Smooth': f"{losses['flow_smoothness'].item():.4f}"
        })

        # Save inference samples at specified batches
        if batch_idx + 1 in args.inference_batches:
            save_inference_samples(model, train_loader, epoch, batch_idx + 1,
                                 args.save_dir, args.inference_samples)

    return stats.get_averages()

def validate_epoch(model, val_loader, epoch, args, device):
    """Validate for one epoch (limited for testing)"""
    model.eval()
    stats = TrainingStats()

    # Limit batches for quick test
    max_batches = min(len(val_loader), args.max_batches_per_epoch // 2)

    with torch.no_grad():
        pbar = tqdm(enumerate(val_loader), total=max_batches,
                    desc=f'Validation {epoch+1} (Quick Test)')

        for batch_idx, batch in pbar:
            if batch_idx >= max_batches:
                break

            # Move to GPU
            inputs = {}
            for key in ['image', 'mask']:
                if key in batch:
                    inputs[key] = [tensor.to(device) for tensor in batch[key]]

            # Forward pass
            losses, prediction, target = model(inputs)

            # Update statistics
            loss_dict = {
                'total': losses['tot'].item(),
                'color': losses['color'].item(),
                'color_gradient': losses['color_gradient'].item(),
                'flow_smoothness': losses['flow_smoothness'].item()
            }
            stats.update(loss_dict)

            # Update progress bar
            pbar.set_postfix({
                'Val Loss': f"{losses['tot'].item():.4f}"
            })

    return stats.get_averages()

def save_checkpoint(model, optimizer, epoch, train_stats, val_stats, args):
    """Save model checkpoint"""
    os.makedirs(args.save_dir, exist_ok=True)

    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_stats': train_stats,
        'val_stats': val_stats,
        'args': vars(args)
    }

    # Save checkpoint
    checkpoint_path = os.path.join(args.save_dir, f'{args.name}_epoch_{epoch:03d}.pth')
    torch.save(checkpoint, checkpoint_path)
    print(f"Checkpoint saved: {checkpoint_path}")

def main():
    args = create_args()

    # Setup GPU
    use_cuda = setup_gpu(args.gpu)
    device = torch.device('cuda' if use_cuda else 'cpu')

    # Create save directory
    os.makedirs(args.save_dir, exist_ok=True)

    print("="*60)
    print("SDCNet2DWithMasks QUICK TEST Training")
    print("="*60)
    print("⚠️  THIS IS A QUICK TEST VERSION")
    print(f"   - Only {args.epochs} epochs")
    print(f"   - Max {args.max_train_samples} training samples")
    print(f"   - Max {args.max_val_samples} validation samples")
    print(f"   - Max {args.max_batches_per_epoch} batches per epoch")
    print("="*60)
    print(f"Device: {device}")
    print(f"Batch size: {args.batch_size}")
    print(f"Learning rate: {args.lr}")
    print("="*60)

    # Create model
    model = SDCNet2DWithMasks(args)
    model = model.to(device)

    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # Create optimizer
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)

    # Create datasets (LIMITED FOR TESTING)
    train_dataset = FrameLoaderWithMasks(args, args.train_file, is_training=True)
    val_dataset = FrameLoaderWithMasks(args, args.val_file, is_training=False)

    # Limit dataset size for quick testing
    if len(train_dataset) > args.max_train_samples:
        train_indices = torch.randperm(len(train_dataset))[:args.max_train_samples]
        train_dataset = Subset(train_dataset, train_indices)

    if len(val_dataset) > args.max_val_samples:
        val_indices = torch.randperm(len(val_dataset))[:args.max_val_samples]
        val_dataset = Subset(val_dataset, val_indices)

    train_loader = DataLoader(train_dataset, batch_size=args.batch_size,
                             shuffle=True, num_workers=args.workers, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=args.val_batch_size,
                           shuffle=False, num_workers=args.workers, pin_memory=True)

    print(f"Training samples: {len(train_dataset)} (limited)")
    print(f"Validation samples: {len(val_dataset)} (limited)")
    print(f"Training batches: {len(train_loader)}")
    print(f"Validation batches: {len(val_loader)}")

    print("\nStarting QUICK TEST training...")

    for epoch in range(args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}")
        print("-" * 50)

        # Train
        train_stats = train_epoch(model, train_loader, optimizer, epoch, args, device)

        # Validate
        val_stats = validate_epoch(model, val_loader, epoch, args, device)

        # Print epoch statistics
        print(f"\nEpoch {epoch+1} Results:")
        print(f"Train - Total: {train_stats['total']:.4f}, Color: {train_stats['color']:.4f}, "
              f"Gradient: {train_stats['color_gradient']:.4f}, Smoothness: {train_stats['flow_smoothness']:.4f}")
        print(f"Val   - Total: {val_stats['total']:.4f}, Color: {val_stats['color']:.4f}, "
              f"Gradient: {val_stats['color_gradient']:.4f}, Smoothness: {val_stats['flow_smoothness']:.4f}")

        # Save checkpoint every epoch for testing
        save_checkpoint(model, optimizer, epoch, train_stats, val_stats, args)

    print("\n" + "="*60)
    print("QUICK TEST COMPLETED SUCCESSFULLY! ✅")
    print("="*60)
    print("The model and training pipeline are working correctly.")
    print("You can now run the full training script with your complete dataset.")
    print(f"Test checkpoints saved in: {args.save_dir}")

if __name__ == "__main__":
    main()
